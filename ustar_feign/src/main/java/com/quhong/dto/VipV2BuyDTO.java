package com.quhong.dto;

import com.quhong.handler.HttpEnvData;

/**
 * VIP V2购买请求DTO
 * <AUTHOR>
 */
public class VipV2BuyDTO extends HttpEnvData {
    /**
     * 要购买的VIP等级
     */
    private int vipLevel;

    private int newGender;

     // 激活vip用的字段
    private int id;
    /**
     * 要激活的VIP卡资源ID
     */
    private int resourceId;

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getNewGender() {
        return newGender;
    }

    public void setNewGender(int newGender) {
        this.newGender = newGender;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getResourceId() {
        return resourceId;
    }

    public void setResourceId(int resourceId) {
        this.resourceId = resourceId;
    }
}
