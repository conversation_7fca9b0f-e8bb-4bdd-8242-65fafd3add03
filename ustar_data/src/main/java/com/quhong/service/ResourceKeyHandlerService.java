package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.JoinSourceDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.JoinSourceData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomCommonScrollMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.room.RoomWebSender;
import com.quhong.room.TestRoomService;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;


@Lazy
@Service
public class ResourceKeyHandlerService {

    public final Logger logger = LoggerFactory.getLogger(getClass());
    private static final Map<Integer, List<String>> ATYPE_TEXT_MAP = new HashMap<Integer, List<String>>() {
        {
            put(MoneyTypeConstant.CATCH_FISH_TYPE, Arrays.asList("Congratulations \u202C%s getting \u202C%s in Fishing King. GO>>"
                    , "  تهانينا ل \u202b%s لحصوله على \u202b%s. هيا بنا>>"));
        }
    };

    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private ActorDao actorDao;
    @Autowired
    protected RoomWebSender roomWebSender;
    @Resource
    private TestRoomService testRoomService;
    @Resource
    private JoinSourceDao joinSourceDao;
    @Resource
    private UserLotteryTicketsService userLotteryTicketsService;

    public ResourceKeyConfigData getConfigData(String resourceKey) {
        return resourceKeyConfigDao.findByKey(resourceKey);
    }

    public List<ResourceKeyConfigData> findListByResourceKeyList(List<String> keyList) {
        return resourceKeyConfigDao.findListByKeyList(keyList);
    }

    private int getResGainType(int resourceType, int resourceId) {
        if (resourceType != BaseDataResourcesConstant.TYPE_RIDE) {
            return 0;
        }
        JoinSourceData joinSourceData = joinSourceDao.findSourceData(resourceId);
        return joinSourceData != null && joinSourceData.getItem_type() == 5 ? 1 : 0;
    }


    /**
     * 通过resource key下发奖励
     */
    public void sendResourceData(String uid, String resourceKey, String titleEn, String desc) {
        sendResourceData(uid, resourceKey, 905, titleEn, "", desc, "", "", 1);
    }

    public boolean sendResourceData(String uid, String resourceKey, String titleEn, String titleAr, String desc, String actionUrl, String broadcastIcon) {
        return sendResourceData(uid, resourceKey, 905, titleEn, titleAr, desc, actionUrl, broadcastIcon, 0);
    }

    public boolean sendResourceData(String uid, String resourceKey, int aType, String titleEn, String titleAr, String desc, String actionUrl, String broadcastIcon, int getWay) {
        return sendResourceData(uid, resourceKey, aType, titleEn, titleAr, desc, desc, actionUrl, broadcastIcon, getWay);
    }

    public boolean sendResourceData(String uid, String resourceKey, int aType, String titleEn, String titleAr, String desc, String diamondDesc, String actionUrl, String broadcastIcon, int getWay) {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
        if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
            logger.error("sendResourceData not find; resourceKey={}", resourceKey);
            return false;
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
            logger.info("sendResourceData toUid={}, resourceMeta={}", uid, JSONObject.toJSONString(resourceMeta));
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_OTHER) {  // 其他奖励下发
                continue;
            } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_COIN) {
                heartRecordDao.changeHeart(uid, resourceMeta.getResourceNumber(), titleEn, desc);
            } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(uid);
                moneyDetailReq.setAtype(aType);
                moneyDetailReq.setChanged(resourceMeta.getResourceNumber());
                moneyDetailReq.setTitle(titleEn);
                moneyDetailReq.setDesc(diamondDesc);
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET) {
                userLotteryTicketsService.addTicketsNum(uid, resourceMeta.getResourceNumber(), titleEn);
            } else {
                ResourcesDTO resourcesDTO = new ResourcesDTO();
                resourcesDTO.setUid(uid);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                    resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
                }
                resourcesDTO.setResId(String.valueOf(resourceMeta.getResourceId()));
                resourcesDTO.setResType(resourceMeta.getResourceType());
                resourcesDTO.setItemsSourceDetail(desc);
                resourcesDTO.setDesc(desc);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE) {
                    resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
                } else {
                    resourcesDTO.setActionType(resourceMeta.getAutoWare() > 0 ? BaseDataResourcesConstant.ACTION_GET_WEAR : BaseDataResourcesConstant.ACTION_GET);
                }
                resourcesDTO.setOfficialMsg(0);
                resourcesDTO.setmTime(DateHelper.getNowSeconds());
                resourcesDTO.setNum(resourceMeta.getResourceNumber() != 0 ? resourceMeta.getResourceNumber() : 1);
                resourcesDTO.setDays(resourceMeta.getResourceTime() != 0 ? resourceMeta.getResourceTime() : -1);
                resourcesDTO.setGetWay(getWay != 0 ? getWay : 1);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_RIDE) {
                    resourcesDTO.setGainType(getResGainType(resourceMeta.getResourceType(), resourceMeta.getResourceId()));
                }
                mqSenderService.asyncHandleResources(resourcesDTO);
            }
            this.pushBroadcastScreenMsg(actorData, resourceMeta, actionUrl, titleEn, titleAr, broadcastIcon);
        }
        return true;
    }

    public void sendOneResourceData(String uid, ResourceKeyConfigData.ResourceMeta resourceMeta,
                                    int aType, String titleEn, String titleAr, String desc, String actionUrl,
                                    String broadcastIcon, int getWay) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            return;
        }
        sendOneResourceData(actorData, resourceMeta, aType, titleEn, titleAr, desc, actionUrl, broadcastIcon, getWay);
    }

    public boolean sendOneResourceData(ActorData actorData, ResourceKeyConfigData.ResourceMeta resourceMeta,
                                       int aType, String titleEn, String titleAr, String desc, String actionUrl,
                                       String broadcastIcon, int getWay) {
        if (actorData == null) {
            return false;
        }
        String uid = actorData.getUid();
        logger.info("sendResourceData toUid={}, resourceMeta={}", uid, JSONObject.toJSONString(resourceMeta));
        if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_OTHER) {  // 其他奖励下发
            return false;
        } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_COIN) {
            heartRecordDao.changeHeart(uid, resourceMeta.getResourceNumber(), titleEn, desc);
        } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(aType);
            moneyDetailReq.setChanged(resourceMeta.getResourceNumber());
            moneyDetailReq.setTitle(titleEn);
            moneyDetailReq.setDesc(desc);
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET) {
            userLotteryTicketsService.addTicketsNum(uid, resourceMeta.getResourceNumber(), titleEn);
        } else {
            ResourcesDTO resourcesDTO = new ResourcesDTO();
            resourcesDTO.setUid(uid);
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
            }
            resourcesDTO.setResId(String.valueOf(resourceMeta.getResourceId()));
            resourcesDTO.setResType(resourceMeta.getResourceType());
            resourcesDTO.setItemsSourceDetail(desc);
            resourcesDTO.setDesc(desc);
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE) {
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
            } else {
                resourcesDTO.setActionType(resourceMeta.getAutoWare() > 0 ? BaseDataResourcesConstant.ACTION_GET_WEAR : BaseDataResourcesConstant.ACTION_GET);
            }
            resourcesDTO.setOfficialMsg(0);
            resourcesDTO.setmTime(DateHelper.getNowSeconds());
            resourcesDTO.setNum(resourceMeta.getResourceNumber() != 0 ? resourceMeta.getResourceNumber() : 1);
            resourcesDTO.setDays(resourceMeta.getResourceTime() != 0 ? resourceMeta.getResourceTime() : -1);
            resourcesDTO.setGetWay(getWay != 0 ? getWay : 1);
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_RIDE) {
                resourcesDTO.setGainType(getResGainType(resourceMeta.getResourceType(), resourceMeta.getResourceId()));
            }
            mqSenderService.asyncHandleResources(resourcesDTO);
        }
        List<String> textList = ATYPE_TEXT_MAP.get(aType);
        if (textList != null) {
            this.pushBroadcastScreenMsg(actorData, resourceMeta, actionUrl, titleEn, titleAr, broadcastIcon,textList.get(0),textList.get(1));
        } else {
            this.pushBroadcastScreenMsg(actorData, resourceMeta, actionUrl, titleEn, titleAr, broadcastIcon);
        }

        return true;
    }

    public boolean sendOneResourceDataNoBroadcast(ActorData actorData, ResourceKeyConfigData.ResourceMeta resourceMeta,
                                                  int aType, String titleEn, String desc, int getWay) {
        if (actorData == null) {
            return false;
        }
        String uid = actorData.getUid();
        logger.info("sendResourceData toUid={}, resourceMeta={}", uid, JSONObject.toJSONString(resourceMeta));
        if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_OTHER) {  // 其他奖励下发
            return false;
        } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_COIN) {
            heartRecordDao.changeHeart(uid, resourceMeta.getResourceNumber(), titleEn, desc);
        } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(aType);
            moneyDetailReq.setChanged(resourceMeta.getResourceNumber());
            moneyDetailReq.setTitle(titleEn);
            moneyDetailReq.setDesc(desc);
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        } else if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET) {
            userLotteryTicketsService.addTicketsNum(uid, resourceMeta.getResourceNumber(), titleEn);
        } else {
            ResourcesDTO resourcesDTO = new ResourcesDTO();
            resourcesDTO.setUid(uid);
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
            }
            resourcesDTO.setResId(String.valueOf(resourceMeta.getResourceId()));
            resourcesDTO.setResType(resourceMeta.getResourceType());
            resourcesDTO.setItemsSourceDetail(desc);
            resourcesDTO.setDesc(desc);
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE) {
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
            } else {
                resourcesDTO.setActionType(resourceMeta.getAutoWare() > 0 ? BaseDataResourcesConstant.ACTION_GET_WEAR : BaseDataResourcesConstant.ACTION_GET);
            }
            resourcesDTO.setOfficialMsg(0);
            resourcesDTO.setmTime(DateHelper.getNowSeconds());
            resourcesDTO.setNum(resourceMeta.getResourceNumber() != 0 ? resourceMeta.getResourceNumber() : 1);
            resourcesDTO.setDays(resourceMeta.getResourceTime() != 0 ? resourceMeta.getResourceTime() : -1);
            resourcesDTO.setGetWay(getWay != 0 ? getWay : 1);
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_RIDE) {
                resourcesDTO.setGainType(getResGainType(resourceMeta.getResourceType(), resourceMeta.getResourceId()));
            }

            // 此麦位框按原来的签到逻辑使用次数过期
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_MIC && resourceMeta.getResourceId() == 121 && getWay == 2) {
                resourcesDTO.setGainType(1);
            }

            mqSenderService.asyncHandleResources(resourcesDTO);
        }
        return true;
    }

    public void pushBroadcastScreenMsg(ActorData actorData, ResourceKeyConfigData.ResourceMeta resourceMeta,
                                       String actionUrl, String titleEn, String titleAr, String prizeIcon) {
        pushBroadcastScreenMsg(actorData, resourceMeta, actionUrl, titleEn, titleAr, prizeIcon, null, null);
    }

    /**
     * 中奖推送广播及公屏消息
     */
    public void pushBroadcastScreenMsg(ActorData actorData, ResourceKeyConfigData.ResourceMeta resourceMeta,
                                       String actionUrl, String titleEn, String titleAr, String prizeIcon,
                                       String textEnTemplate, String textArTemplate) {
        String roomId = null;
        int allRoomScreen = resourceMeta.getAllRoomScreen();
        int inRoomScreen = resourceMeta.getInRoomScreen();
        int broadcast = resourceMeta.getBroadcast();

        String uid = actorData.getUid();
        if (titleEn.startsWith("test") && (allRoomScreen > 0 || inRoomScreen > 0 || broadcast > 0)) {
            roomId = testRoomService.getTestRoom();
        }

        if (allRoomScreen > 0 && StringUtils.isEmpty(roomId)) {
            String myRoomId = roomPlayerRedis.getActorRoomStatus(uid);
            roomId = StringUtils.isEmpty(myRoomId) ? RoomWebSender.ALL_ROOM : RoomWebSender.ALL_ROOM + "_" + myRoomId;
        }

        if (inRoomScreen > 0 && StringUtils.isEmpty(roomId)) {
            roomId = roomPlayerRedis.getActorRoomStatus(uid);
        }

        // 推送公屏消息
        if (!StringUtils.isEmpty(roomId)) {
            List<HighlightTextObject> list = new ArrayList<>();
            HighlightTextObject object = new HighlightTextObject();
            object.setText(actorData.getName());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(resourceMeta.getResourceNameEn());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(resourceMeta.getResourceNameAr());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText("GO>>");
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText("هيا بنا>>");
            object.setHighlightColor("#FFE200");
            list.add(object);

            RoomNotificationMsg msg = new RoomNotificationMsg();
            msg.setUid(uid);
            msg.setUser_name(actorData.getName());
            msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            if (StringUtils.isEmpty(textEnTemplate) || StringUtils.isEmpty(textArTemplate)) {
                textEnTemplate = "Congratulations \u202C%s getting \u202C%s. GO>>";
                textArTemplate = "  تهانينا ل \u202b%s لحصوله على \u202b%s. هيا بنا>>";
            }
            msg.setText(String.format(textEnTemplate, actorData.getName(), resourceMeta.getResourceNameEn()));
            msg.setText_ar(String.format(textArTemplate, actorData.getName(), resourceMeta.getResourceNameAr()));
            msg.setHighlight_text(list);
            msg.setHighlight_text_ar(list);
            msg.setWeb_url(actionUrl);
            msg.setWeb_type(1);
            msg.setWidth(375);
            msg.setHeight(560);
            roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
        }

        if (broadcast > 0) {
            RoomCommonScrollMsg scrollMsg = new RoomCommonScrollMsg();
            scrollMsg.setUid(uid);
            scrollMsg.setPrizeIcon(prizeIcon);
            scrollMsg.setPrizeTextEn(String.format("Congratulations to %s for getting %s in %s", actorData.getName(), resourceMeta.getResourceNameEn(), titleEn));
            scrollMsg.setPrizeTextAr(String.format("تهانينا لـ %s للحصول على %s في %s", actorData.getName(), resourceMeta.getResourceNameAr(), titleAr));

            List<HighlightTextObject> list = new ArrayList<>();
            HighlightTextObject object = new HighlightTextObject();
            object.setText(actorData.getName());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(titleEn);
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(titleAr);
            object.setHighlightColor("#FFE200");
            list.add(object);
            scrollMsg.setHighlightTextEn(list);
            scrollMsg.setHighlightTextAr(list);
            scrollMsg.setActionType(StringUtils.isEmpty(actionUrl) ? 998 : 19);
            scrollMsg.setActionValue(actionUrl);
            logger.info("RoomCommonScrollMsg: {}, roomId:{}", JSONObject.toJSONString(scrollMsg), roomId);
            roomWebSender.sendRoomWebMsg(StringUtils.isEmpty(roomId) ? "all" : roomId, uid, scrollMsg, false);
        }
    }

    /**
     * vip专用移除资源
     */
    public boolean removeResourceData(String uid, String resourceKey, String desc, int vipEndTime) {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
        if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
            logger.error("sendResourceData not find; resourceKey={}", resourceKey);
            return false;
        }
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
            int resType = resourceMeta.getResourceType();
            if (resType == BaseDataResourcesConstant.TYPE_OTHER || resType == BaseDataResourcesConstant.TYPE_COIN || resType == BaseDataResourcesConstant.TYPE_DIAMOND || resType == BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET) {  // 其他奖励下发
                continue;
            } else {
                ResourcesDTO resourcesDTO = new ResourcesDTO();
                resourcesDTO.setUid(uid);
                if (resType == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                    resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
                }
                resourcesDTO.setResId(String.valueOf(resourceMeta.getResourceId()));
                resourcesDTO.setResType(resType);
                resourcesDTO.setItemsSourceDetail(desc);
                resourcesDTO.setDesc(desc);
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_DELETE);
                resourcesDTO.setOfficialMsg(0);
                resourcesDTO.setmTime(DateHelper.getNowSeconds());
                resourcesDTO.setNum(resourceMeta.getResourceNumber() != 0 ? resourceMeta.getResourceNumber() : 1);
                resourcesDTO.setDays(resourceMeta.getResourceTime() != 0 ? resourceMeta.getResourceTime() : -1);
                resourcesDTO.setGetWay(0);
                resourcesDTO.setCancelWarn(1);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_RIDE) {
                    resourcesDTO.setGainType(getResGainType(resourceMeta.getResourceType(), resourceMeta.getResourceId()));
                }
                if (resType == BaseDataResourcesConstant.TYPE_ROOM_LOCK) {
                    resourcesDTO.setSetEndTime(vipEndTime);
                }
                logger.info("removeResourceData toUid={}, resourcesDTO={}", uid, JSONObject.toJSONString(resourcesDTO));
                mqSenderService.asyncHandleResources(resourcesDTO);
            }
        }
        return true;
    }

    public boolean wearResourceData(String uid, String resourceKey, String desc) {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
        if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
            logger.error("sendResourceData not find; resourceKey={}", resourceKey);
            return false;
        }
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
            logger.info("removeResourceData toUid={}, resourceMeta={}", uid, JSONObject.toJSONString(resourceMeta));
            if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_OTHER || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_COIN
                    || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND
                    || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET) {  // 其他奖励下发
                continue;
            } else {
                ResourcesDTO resourcesDTO = new ResourcesDTO();
                resourcesDTO.setUid(uid);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                    resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
                }
                resourcesDTO.setResId(String.valueOf(resourceMeta.getResourceId()));
                resourcesDTO.setResType(resourceMeta.getResourceType());
                resourcesDTO.setItemsSourceDetail(desc);
                resourcesDTO.setDesc(desc);
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_WEAR);
                resourcesDTO.setOfficialMsg(0);
                resourcesDTO.setmTime(DateHelper.getNowSeconds());
                resourcesDTO.setNum(resourceMeta.getResourceNumber() != 0 ? resourceMeta.getResourceNumber() : 1);
                resourcesDTO.setDays(resourceMeta.getResourceTime() != 0 ? resourceMeta.getResourceTime() : -1);
                resourcesDTO.setGetWay(0);
                resourcesDTO.setCancelWarn(1);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_RIDE) {
                    resourcesDTO.setGainType(getResGainType(resourceMeta.getResourceType(), resourceMeta.getResourceId()));
                }
                mqSenderService.asyncHandleResources(resourcesDTO);
            }
        }
        return true;
    }


}
