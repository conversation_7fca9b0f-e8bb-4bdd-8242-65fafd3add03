package com.quhong.data.dto;


import com.quhong.enums.BaseDataResourcesConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Set;

public class ResourcesDTO {
    private static final Logger logger = LoggerFactory.getLogger(ResourcesDTO.class);
    private String uid;
    private String recordId;    // 记录id: 资源在用户获得资源表的唯一id(某些资源对同一个资源ResId有多条记录，需要记录不同的流水id, 更新和删除需要用到)
    private String resId;
    /**
     * https://www.apifox.cn/web/project/782819/apis/api-27024466
     *
     * @see BaseDataResourcesConstant
     * <p>
     * 注意：坐骑类型,resId小于100只能是按自然天过期资源(GAIN_TYPE_DAY),
     * resId大于等于100只能是按使用次数过期资源(GAIN_TYPE_USE)
     * 用法与resId不匹配不能下发
     *
     * 个人资源背景类型，resId只能小于1000，并且主题资源类型不能为默认(type=0),与vip类型(type=1)才能下发
     */
    private Integer resType;  // 资源类型 1 勋章 2 麦位框 3 坐骑 4 背包礼物  5 房间锁 6 聊天气泡 7 声波纹 8 浮屏 9 个人背景资源
    private Integer days;    // 资源下发天数，-1为永久
    private Integer actionType; //  1 仅下发资源 2 下发且佩戴 3 仅佩戴 4 删除资源 7 取消佩戴
    private Integer seconds; // 测试环境可用 资源下秒数，-1为永久
    /**
     * 对应数数字段 items_source_detail
     */
    private String itemsSourceDetail ; //数数埋点游戏title字段，具体业务由产品定

    private String desc = ""; // 描述 比如 “某某活动第一名”
    private Integer mTime; // 时间戳，单位秒
    private Integer gainType; // 0 自然天流失 1 使用次数减少
    private String id; //请求id
    /**
     * 对应数数字段 items_source，默认为1
     * 获得途径  0:未知  1: 运营活动获得 2 签到获得 3 admin发放
     * 4 游戏奖励 5 充值奖励 6 行为奖励 7 购买获得 8 成就奖励 9 他人购买赠与获得
     */
    private int getWay = 1;
    private int num; // 获得的资源数量，目前只有背包礼物有
    private Set<String> removeList;
    private String roomId;
    /**
     * 仅下发类型(ACTION_GET)有效（ps：商店购买(2 麦位框, 3 坐骑,6 聊天气泡 7 声波纹 8 浮屏)类型有效），
     * 1 没有任何佩戴资源佩戴，则佩戴 ;
     * 0 没有任何佩戴资源佩戴，也不佩戴;
     * 默认为 0
     */
    private int emptyWearType;

    /**
     * 获得靓号的荣誉等级
     * 仅资源类型为靓号资源有效(TYPE_BEAUTIFUL_RID)
     */
    private int honorLevel;

    /**
     * 荣誉靓号等级特殊权限
     * 第一次申请靓号，只要荣誉等级大于等于4级 就立马给一次改靓号的权限
     */
    private Integer canChangeBeautifulRid;

    /**
     * 荣誉靓号等级特殊字段
     * 是否延续靓号使用时间
     *
     * 对于房间锁：vip移除时删除房间锁，判断是否VIP下发的房间锁，如果是则删除(判断VIP结束时间与房间锁结束时间差是否只相差1分钟)
     */
    private int setEndTime = 1;

    /**
     * getWay 为他人购买赠与时，赠与用户的uid
     */
    private String fromUid;

    private int officialMsg;   // 是否官方消息推送
    private Integer beans; //资源的售价(注：但资源为充值优惠卷时，这个字段为充值钻石数)

    /**
     * 靓号的等级
     * 仅资源类型为靓号资源有效(TYPE_BEAUTIFUL_RID)
     */
    private int alphaLevel;

    /**
     * 优惠劵使用时的充值金额
     */
    private BigDecimal money;
    /**
     * 取消失败告警
     */
    private int cancelWarn;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getResId() {
        return resId;
    }

    public void setResId(String resId) {
        this.resId = resId;
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Integer getSeconds() {
        return seconds;
    }

    public void setSeconds(Integer seconds) {
        this.seconds = seconds;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getmTime() {
        return mTime;
    }

    public void setmTime(Integer mTime) {
        this.mTime = mTime;
    }

    public Integer getActionType() {
        return actionType;
    }

    public void setActionType(Integer actionType) {
        this.actionType = actionType;
    }

    public Integer getGainType() {
        return gainType == null ? 0 : gainType;
    }

    public void setGainType(Integer gainType) {
        this.gainType = gainType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getGetWay() {
        return getWay;
    }

    public void setGetWay(int getWay) {
        this.getWay = getWay;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public Set<String> getRemoveList() {
        return removeList;
    }

    public void setRemoveList(Set<String> removeList) {
        this.removeList = removeList;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getItemsSourceDetail() {
        return itemsSourceDetail;
    }

    public void setItemsSourceDetail(String itemsSourceDetail) {
        this.itemsSourceDetail = itemsSourceDetail;
    }

    public int getEmptyWearType() {
        return emptyWearType;
    }

    public void setEmptyWearType(int emptyWearType) {
        this.emptyWearType = emptyWearType;
    }

    public int getHonorLevel() {
        return honorLevel;
    }

    public void setHonorLevel(int honorLevel) {
        this.honorLevel = honorLevel;
    }

    public Integer getCanChangeBeautifulRid() {
        return canChangeBeautifulRid;
    }

    public void setCanChangeBeautifulRid(Integer canChangeBeautifulRid) {
        this.canChangeBeautifulRid = canChangeBeautifulRid;
    }

    public int getSetEndTime() {
        return setEndTime;
    }

    public void setSetEndTime(int setEndTime) {
        this.setEndTime = setEndTime;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public int getOfficialMsg() {
        return officialMsg;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public void setOfficialMsg(int officialMsg) {
        this.officialMsg = officialMsg;
    }

    public int getAlphaLevel() {
        return alphaLevel;
    }

    public void setAlphaLevel(int alphaLevel) {
        this.alphaLevel = alphaLevel;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public int getCancelWarn() {
        return cancelWarn;
    }

    public void setCancelWarn(int cancelWarn) {
        this.cancelWarn = cancelWarn;
    }

    public boolean isParmsValid() {
        if (this.getResType() == BaseDataResourcesConstant.TYPE_ROOM_LOCK && StringUtils.isEmpty(this.getResId())) {
            this.setResId(String.valueOf(BaseDataResourcesConstant.DEFAULT_ROOM_LOCK_ID));
        }

        if (this.getResType() != BaseDataResourcesConstant.TYPE_MIC &&
                this.getResType() != BaseDataResourcesConstant.TYPE_RIDE &&
                this.getGainType() == BaseDataResourcesConstant.GAIN_TYPE_USE) {
            logger.error("only mic and ride can set type use");
            return false;
        }

        if (this.getResType() == BaseDataResourcesConstant.TYPE_BAG_GIFT && num < 0) {
            logger.error("add bag gift num can not low 0");
            return false;
        }

        if (this.getResType() == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND && StringUtils.isEmpty(roomId)) {
            logger.error("uid={} resId={} roomId is empty", uid, resId);
            return false;
        }

        if (StringUtils.isEmpty(this.getResId()) || StringUtils.isEmpty(this.getUid())
                || null == this.getResType() || null == this.getActionType()
                || null == this.getmTime()) {
            return false;
        }
        if (this.getActionType() == BaseDataResourcesConstant.ACTION_GET || this.getActionType() == BaseDataResourcesConstant.ACTION_GET_WEAR) {
            if (this.getResType() == BaseDataResourcesConstant.TYPE_BEAUTIFUL_RID) {
                return null != this.getDays() && this.getDays() >= -1 && this.getDays() <= 999;
            } else {
                return null != this.getDays() && this.getDays() >= -1 && this.getDays() <= 999 && this.getDays() != 0;
            }
        }
        return true;
    }


    @Override
    public String toString() {
        return "ResourcesDTO{" +
                "uid='" + uid + '\'' +
                ", resId='" + resId + '\'' +
                ", resType=" + resType +
                ", days=" + days +
                ", actionType=" + actionType +
                ", seconds=" + seconds +
                ", itemsSourceDetail='" + itemsSourceDetail + '\'' +
                ", desc='" + desc + '\'' +
                ", mTime=" + mTime +
                ", gainType=" + gainType +
                ", id='" + id + '\'' +
                ", getWay=" + getWay +
                ", num=" + num +
                ", removeList=" + removeList +
                ", roomId='" + roomId + '\'' +
                ", emptyWearType=" + emptyWearType +
                ", honorLevel=" + honorLevel +
                ", canChangeBeautifulRid=" + canChangeBeautifulRid +
                ", setEndTime=" + setEndTime +
                ", fromUid='" + fromUid + '\'' +
                ", officialMsg='" + officialMsg + '\'' +
                ", beans='" + beans + '\'' +
                '}';
    }
}
