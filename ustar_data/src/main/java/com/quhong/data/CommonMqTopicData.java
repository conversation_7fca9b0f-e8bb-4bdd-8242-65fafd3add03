package com.quhong.data;

import com.quhong.core.utils.DateHelper;

import java.util.List;

public class CommonMqTopicData {

    /**
     * 触发事件uid
     */
    private String uid;

    /**
     * 触发事件所在房间
     */
    private String roomId;

    /**
     * 如果事件是相互的，则aid存在, 否则为空
     */
    private String aid;

    /**
     * 防止重复的标记 如：游戏id，动态id
     * 1、赢者uid  =>  uid和aid玩pk 赢者赋值handleId
     */
    private String handleId;

    /**
     * 事件类型
     */
    private String item;

    /**
     * 事件所产生值, 如上麦时长
     */
    private int value;

    /**
     * 事件所产生值, 冗余字段, 可以填充其他value
     */
    private int remainValue;

    /**
     * 事件发生日期YYYY-MM-DD(沙特时区)
     */
    private String dateStr;

    /**
     * 事件发生时间戳
     */
    private long timeStamp;


    private List<String> dataList ;

    /**
     * 冗余字段, 可以填充其他value、 json数据
     */
    private String jsonData;

    public static class StarBeatGameInfo{
        private Integer resourceType;
        private Integer resourceId;
        private Integer resourceNum;
        private Integer resourceTime;

        public Integer getResourceType() {
            return resourceType;
        }

        public void setResourceType(Integer resourceType) {
            this.resourceType = resourceType;
        }

        public Integer getResourceId() {
            return resourceId;
        }

        public void setResourceId(Integer resourceId) {
            this.resourceId = resourceId;
        }

        public Integer getResourceNum() {
            return resourceNum;
        }

        public void setResourceNum(Integer resourceNum) {
            this.resourceNum = resourceNum;
        }

        public Integer getResourceTime() {
            return resourceTime;
        }

        public void setResourceTime(Integer resourceTime) {
            this.resourceTime = resourceTime;
        }
    }

    public static class WinGameInfo{
        private Integer currencyType;    // 游戏币类型 1 心心 2 钻石
        private Integer awardValue;      // 赢得钻石数

        public Integer getCurrencyType() {
            return currencyType;
        }

        public void setCurrencyType(Integer currencyType) {
            this.currencyType = currencyType;
        }

        public Integer getAwardValue() {
            return awardValue;
        }

        public void setAwardValue(Integer awardValue) {
            this.awardValue = awardValue;
        }
    }

    public static class PlayGameInfo{
        private int currencyType;    // 游戏币类型 1 心心 2 钻石
        private int currencyValue;   // 货币数量

        public PlayGameInfo() {
        }

        public PlayGameInfo(int currencyType, int currencyValue) {
            this.currencyType = currencyType;
            this.currencyValue = currencyValue;
        }

        public int getCurrencyType() {
            return currencyType;
        }

        public void setCurrencyType(int currencyType) {
            this.currencyType = currencyType;
        }

        public int getCurrencyValue() {
            return currencyValue;
        }

        public void setCurrencyValue(int currencyValue) {
            this.currencyValue = currencyValue;
        }
    }


    public CommonMqTopicData() {
    }

    public CommonMqTopicData(String uid, String roomId, String aid, String handleId, String item, int value) {
        this.uid = uid;
        this.roomId = roomId;
        this.aid = aid;
        this.handleId = handleId;
        this.item = item;
        this.value = value;
        this.dateStr = DateHelper.ARABIAN.formatDateInDay();
        this.timeStamp = System.currentTimeMillis();
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getHandleId() {
        return handleId;
    }

    public void setHandleId(String handleId) {
        this.handleId = handleId;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getRemainValue() {
        return remainValue;
    }

    public void setRemainValue(int remainValue) {
        this.remainValue = remainValue;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public List<String> getDataList() {
        return dataList;
    }

    public void setDataList(List<String> dataList) {
        this.dataList = dataList;
    }

    public String getJsonData() {
        return jsonData;
    }

    public void setJsonData(String jsonData) {
        this.jsonData = jsonData;
    }
}
