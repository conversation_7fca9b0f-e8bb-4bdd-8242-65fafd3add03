package com.quhong.analysis;

/**
 * vip体验卡 的 增加/减少  数据在此记录
 */
public class ItemsChangeEvent extends UserEvent {
    /**
     * 变动行为
     * 1 增加
     * 2 减少
     */
    public int change_action;
    /**
     * 资源类型
     */
    public int items_type;
    /**
     * 资源id
     */
    public int items_id;
    /**
     * 资源名称
     */
    public String items_name;
    /**
     * 资源使用期限
     */
    public int items_service_life;
    /**
     * 资源增加类型
     * 只有变动行为=增加时 有值：(把目前各种来源的发放都在此记录，后续有新增在此补上)
     * 1 后台发放
     * 2
     * 3
     * ...
     */
    public int items_add_type;
    /**
     * 资源增加描述
     */
    public String items_add_desc;
    /**
     * 资源减少类型
     * 只有变动行为=减少时 有值：
     * 1 激活使用
     * 2 过期
     * ...
     */
    public int items_reduce_type;
    /**
     * 资源减少描述
     */
    public String items_reduce_desc;
    /**
     * 变动数量
     */
    public int change_nums;

    public int ctime;               // 数据创建时间

    @Override
    public String getEventName() {
        return "items_change_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getChange_action() {
        return change_action;
    }

    public void setChange_action(int change_action) {
        this.change_action = change_action;
    }

    public int getItems_type() {
        return items_type;
    }

    public void setItems_type(int items_type) {
        this.items_type = items_type;
    }

    public int getItems_id() {
        return items_id;
    }

    public void setItems_id(int items_id) {
        this.items_id = items_id;
    }

    public String getItems_name() {
        return items_name;
    }

    public void setItems_name(String items_name) {
        this.items_name = items_name;
    }

    public int getItems_service_life() {
        return items_service_life;
    }

    public void setItems_service_life(int items_service_life) {
        this.items_service_life = items_service_life;
    }

    public int getItems_add_type() {
        return items_add_type;
    }

    public void setItems_add_type(int items_add_type) {
        this.items_add_type = items_add_type;
    }

    public String getItems_add_desc() {
        return items_add_desc;
    }

    public void setItems_add_desc(String items_add_desc) {
        this.items_add_desc = items_add_desc;
    }

    public int getItems_reduce_type() {
        return items_reduce_type;
    }

    public void setItems_reduce_type(int items_reduce_type) {
        this.items_reduce_type = items_reduce_type;
    }

    public String getItems_reduce_desc() {
        return items_reduce_desc;
    }

    public void setItems_reduce_desc(String items_reduce_desc) {
        this.items_reduce_desc = items_reduce_desc;
    }

    public int getChange_nums() {
        return change_nums;
    }

    public void setChange_nums(int change_nums) {
        this.change_nums = change_nums;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
