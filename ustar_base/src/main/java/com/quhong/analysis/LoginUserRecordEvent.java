package com.quhong.analysis;

import java.math.BigDecimal;

/**
 * 登录的用户信息
 *
 * <AUTHOR>
 * @date 2022/5/24
 */
public class LoginUserRecordEvent extends UserEvent {
    public int beans;  // 钻石余额
    public int fb_gender;  // 性别  1男 2女
    public int age;    // 年龄
    public int login_type; // 登录类型
    public int accept_talk;
    public String ip;  // 用户ip
    public String tn_id;  // 设备ID(图灵盾)
    public String channel;  // 安装商店来源
    public int is_face;
    public String app_package_name;  // 包名
    public int version_code;  // 版本号
    public int heart_got;  // 爱心余额
    public String idfa;  // 苹果广告ID
    public String os;  // 系统
    public int rid;  // 前端展示的rid
    public int recharge;
    public int generation_time;  // 注册时间
    public String ta_device_id;
    public int friends_number;  // 好友数
    public int following_number;  // 关注数
    public int followers_number;  // 粉丝数
    public String phone;  // 用户手机号
    public String gaid;  // 广告ID
    public BigDecimal recharge_total;  // 总充值金额
    public String birthday;  // 用户生日
    public String dynamic_channel; // 客户端上报字段
    public String shu_mei_id;
    public int lucky_game_switch_status;  // bc游戏开关状态
    private String version_name;

    @Override
    public String getEventName() {
        return "login_user_info";
    }

    @Override
    public int getEventTime() {
        return 0;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getFb_gender() {
        return fb_gender;
    }

    public void setFb_gender(int fb_gender) {
        this.fb_gender = fb_gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getLogin_type() {
        return login_type;
    }

    public void setLogin_type(int login_type) {
        this.login_type = login_type;
    }

    public int getAccept_talk() {
        return accept_talk;
    }

    public void setAccept_talk(int accept_talk) {
        this.accept_talk = accept_talk;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getIs_face() {
        return is_face;
    }

    public void setIs_face(int is_face) {
        this.is_face = is_face;
    }

    public String getApp_package_name() {
        return app_package_name;
    }

    public void setApp_package_name(String app_package_name) {
        this.app_package_name = app_package_name;
    }

    public int getVersion_code() {
        return version_code;
    }

    public void setVersion_code(int version_code) {
        this.version_code = version_code;
    }

    public int getHeart_got() {
        return heart_got;
    }

    public void setHeart_got(int heart_got) {
        this.heart_got = heart_got;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getRecharge() {
        return recharge;
    }

    public void setRecharge(int recharge) {
        this.recharge = recharge;
    }

    public int getGeneration_time() {
        return generation_time;
    }

    public void setGeneration_time(int generation_time) {
        this.generation_time = generation_time;
    }

    public String getTa_device_id() {
        return ta_device_id;
    }

    public void setTa_device_id(String ta_device_id) {
        this.ta_device_id = ta_device_id;
    }

    public int getFriends_number() {
        return friends_number;
    }

    public void setFriends_number(int friends_number) {
        this.friends_number = friends_number;
    }

    public int getFollowing_number() {
        return following_number;
    }

    public void setFollowing_number(int following_number) {
        this.following_number = following_number;
    }

    public int getFollowers_number() {
        return followers_number;
    }

    public void setFollowers_number(int followers_number) {
        this.followers_number = followers_number;
    }

    public String getPhone() { return phone; }

    public void setPhone(String phone) { this.phone = phone; }

    public String getGaid() { return gaid; }

    public void setGaid(String gaid) { this.gaid = gaid; }

    public BigDecimal getRecharge_total() { return recharge_total; }

    public void setRecharge_total(BigDecimal recharge_total) { this.recharge_total = recharge_total; }

    public String getBirthday() { return birthday; }

    public void setBirthday(String birthday) { this.birthday = birthday; }

    public String getDynamic_channel() {
        return dynamic_channel;
    }

    public void setDynamic_channel(String dynamic_channel) {
        this.dynamic_channel = dynamic_channel;
    }

    public String getShu_mei_id() {
        return shu_mei_id;
    }

    public void setShu_mei_id(String shu_mei_id) {
        this.shu_mei_id = shu_mei_id;
    }

    public int getLucky_game_switch_status() {
        return lucky_game_switch_status;
    }

    public void setLucky_game_switch_status(int lucky_game_switch_status) {
        this.lucky_game_switch_status = lucky_game_switch_status;
    }

    public String getVersion_name() {
        return version_name;
    }

    public void setVersion_name(String version_name) {
        this.version_name = version_name;
    }
}
