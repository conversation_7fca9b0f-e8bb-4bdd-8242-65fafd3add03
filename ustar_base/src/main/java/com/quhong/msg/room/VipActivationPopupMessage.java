package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.proto.YoustarProtoHighlightText;
import com.quhong.proto.YoustarProtoUser;

import java.util.ArrayList;
import java.util.List;

@Message(cmd = Cmd.VIP_ACTIVATION_POP_UP_MSG)
public class VipActivationPopupMessage extends MarsServerMsg {
    private String uid;
    private int vipLevel;
    private String popupMedal;
    private String activationTextEn;
    private String activationTextAr;
    private List<HighlightTextObject> highlightTextEn;
    private List<HighlightTextObject> highlightTextAr;
    private int expireTime;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoUser.VipActivationPopupMessage msg = YoustarProtoUser.VipActivationPopupMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.uid = msg.getUid();
        this.vipLevel = msg.getVipLevel();
        this.popupMedal = msg.getPopupMedal();
        this.activationTextEn = msg.getActivationTextEn();
        this.activationTextAr = msg.getActivationTextAr();
        this.highlightTextEn = new ArrayList<>();
        for (YoustarProtoHighlightText.HighlightText highlightText : msg.getHighlightTextEnList()) {
            HighlightTextObject highlightTextObject = new HighlightTextObject();
            highlightTextObject.doFromBody(highlightText);
            this.highlightTextEn.add(highlightTextObject);
        }
        this.highlightTextAr = new ArrayList<>();
        for (YoustarProtoHighlightText.HighlightText highlightText : msg.getHighlightTextArList()) {
            HighlightTextObject highlightTextObject = new HighlightTextObject();
            highlightTextObject.doFromBody(highlightText);
            this.highlightTextAr.add(highlightTextObject);
        }
        this.expireTime = msg.getExpireTime();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoUser.VipActivationPopupMessage.Builder builder = YoustarProtoUser.VipActivationPopupMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setUid(this.uid == null ? "" : this.uid);
        builder.setVipLevel(this.vipLevel);
        builder.setPopupMedal(this.popupMedal == null ? "" : this.popupMedal);
        builder.setActivationTextEn(this.activationTextEn == null ? "" : this.activationTextEn);
        builder.setActivationTextAr(this.activationTextAr == null ? "" : this.activationTextAr);
        if(this.highlightTextEn != null){
            for (HighlightTextObject highlightTextObject : highlightTextEn) {
                builder.addHighlightTextEn(highlightTextObject.doToBody());
            }
        }
        if(this.highlightTextAr != null){
            for (HighlightTextObject highlightTextObject : highlightTextAr) {
                builder.addHighlightTextAr(highlightTextObject.doToBody());
            }
        }
        builder.setExpireTime(this.expireTime);
        return builder.build().toByteArray();
    }

    @Override
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getPopupMedal() {
        return popupMedal;
    }

    public void setPopupMedal(String popupMedal) {
        this.popupMedal = popupMedal;
    }

    public String getActivationTextEn() {
        return activationTextEn;
    }

    public void setActivationTextEn(String activationTextEn) {
        this.activationTextEn = activationTextEn;
    }

    public String getActivationTextAr() {
        return activationTextAr;
    }

    public void setActivationTextAr(String activationTextAr) {
        this.activationTextAr = activationTextAr;
    }

    public List<HighlightTextObject> getHighlightTextEn() {
        return highlightTextEn;
    }

    public void setHighlightTextEn(List<HighlightTextObject> highlightTextEn) {
        this.highlightTextEn = highlightTextEn;
    }

    public List<HighlightTextObject> getHighlightTextAr() {
        return highlightTextAr;
    }

    public void setHighlightTextAr(List<HighlightTextObject> highlightTextAr) {
        this.highlightTextAr = highlightTextAr;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }
}
