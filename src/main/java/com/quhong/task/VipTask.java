package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.VipAllConfig;
import com.quhong.deliver.VipExpireTaskDeliver;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.redis.IdentifyRedis;
import com.quhong.service.VipV2Service;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("SynchronizeOnNonFinalField")
@Component
public class VipTask {

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private VipExpireTaskDeliver vipExpireTaskDeliver;
    @Resource
    private VipV2Service vipV2Service;

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 */5 * * * ?")
    public void vipExpireTask() {

        if (k8sUtils.isMasterFromCache()) {
            synchronized (vipExpireTaskDeliver) {
                vipExpireTaskDeliver.vipExpireTask();
                vipV2Service.handleVipModifyCountryTask();
            }
        }
    }

    /**
     * 每天北京时间：6:00 执行一次, sh任务埋点
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0 22 * * ?")
    public void userVipInfoReport() {
        if (k8sUtils.isMasterFromCache()) {
            synchronized (vipExpireTaskDeliver) {
                vipExpireTaskDeliver.dailyVipUserInfoReport();
            }
        }
    }
}
