package com.quhong.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.DailyVipUserEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.ItemsChangeEvent;
import com.quhong.cache.CacheMap;
import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.constant.ResourceOfficialMsgConstant;
import com.quhong.constant.VipFeatureConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CheckExpireData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ResourceConfigDao;
import com.quhong.mongo.data.ResourceConfigData;
import com.quhong.msg.room.VipCardNumMsg;
import com.quhong.mysql.dao.UserResourceDao;
import com.quhong.mysql.data.ResourcesDetail;
import com.quhong.mysql.data.UserResourceData;
import com.quhong.mysql.data.VipUserInfoData;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Component
public class VipCardResourcesHandler extends BaseResourcesHandler {

    private static final Logger logger = LoggerFactory.getLogger(VipCardResourcesHandler.class);
    private static final String VIP_RECORD_URL = ServerConfig.isProduct() ? "https://static.youstar.live/vip_record/?fullScreen=1" : "https://test2.qmovies.tv/vip_record/?fullScreen=1";
    private final CacheMap<String, CheckExpireData> cacheMap;
    private static final int VIP_CARD_PRINCE_ID = ServerConfig.isProduct() ? 115 : 115;
    private static final int VIP_CARD_KING_ID = ServerConfig.isProduct() ? 116 : 116;
    private static final int VIP_CARD_QUEEN_ID = ServerConfig.isProduct() ? 117 : 117;

    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ActorConfigDao actorConfigDao;

    public VipCardResourcesHandler() {
        cacheMap = new CacheMap<>(EXPIRE_TIME_DEFAULT);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    /**
     * 用户获得vipCard资源
     */
    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        ActorData actorData = actorDao.getActorData(resourcesDetail.getUid());
        int gender = actorData.getFb_gender();
        List<ResourceConfigData> resConfigDataList = resourceConfigDao.getResourceAllListFromDb(BaseDataResourcesConstant.TYPE_VIP_CARD);
        Map<Integer, ResourceConfigData> vipLevelResConfigDataMap = resConfigDataList.stream().collect(Collectors.toMap(ResourceConfigData::getRedundantField, Function.identity()));
        resourcesDetail.setResId(String.valueOf(resId));
        ResourceConfigData data = resourceConfigDao.getResourceDataFromDb(resId, resourcesDetail.getResType());
        if (null == data) {
            logger.error("not find resource data. resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        int resVipLevel = data.getRedundantField();
        if (gender == 2 && (resVipLevel == VipFeatureConstant.VIP_LEVEL_5 || resVipLevel == VipFeatureConstant.VIP_LEVEL_6)) {
            // 女性不可拥有VIP5、VIP6卡，将其升级为Queen VIP卡
            ResourceConfigData queenConfig = vipLevelResConfigDataMap.get(VipFeatureConstant.VIP_LEVEL_QUEEN);
            if (queenConfig == null) {
                logger.error("VIP card config not found for level 10");
                return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
            }
            resId = queenConfig.getResourceId();
        }

        if (gender == 1 && resVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
            ResourceConfigData princeConfig = vipLevelResConfigDataMap.get(VipFeatureConstant.VIP_LEVEL_5);
            if (princeConfig == null) {
                logger.error("VIP card config not found for level 5");
                return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
            }
            resId = princeConfig.getResourceId();
        }

        int num = resourcesDetail.getNum() != null ? resourcesDetail.getNum() : 0;
        if(num <= 0){
            logger.error("VipCardResourcesHandler num is error resourcesDetail:{}", resourcesDetail);
            return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
        }
        resourcesDetail.setName(data.getName());
        String uid = resourcesDetail.getUid();
        int days = resourcesDetail.getDays();
        int currentTime = DateHelper.getNowSeconds();
        long endTime = days <= 15 ? currentTime + 30 * 86400L : currentTime + 40 * 86400L;
        List<UserResourceData> dataList = generateUserResourceData(uid, resId, resourcesDetail.getResType(), endTime, num, resourcesDetail.getGetWay(), days);
        userResourceDao.insertMany(uid, dataList);
        sendVipCardNumMsg(uid, num);
        itemChangesReport(uid, 1, resId, data.getName(), days, 1, 0, num);
        return ApiResult.getOk();
    }

    private List<UserResourceData> generateUserResourceData(String uid, int resId, int resType, long endTime, int num,  int getWay, int days) {
        UserResourceData data = new UserResourceData();
        data.setUid(uid);
        data.setResourceId(resId);
        data.setResourceType(resType);
        data.setStatus(0);
        data.setResourceNumber(days);
        data.setEndTime(endTime);
        data.setResourceOrigin(getWay);
        data.setCtime(DateHelper.getNowSeconds());
        return new ArrayList<>(Collections.nCopies(num, data));
    }

    /**
     * 用户设置使用vipCard资源
     */
    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        String recordId = resourcesDetail.getRecordId();
        if (ObjectUtils.isEmpty(recordId)){
            logger.error("VipCardResourcesHandler recordId is empty resourcesDetail:{}", JSONObject.toJSONString(resourcesDetail));
            return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
        }

        int recordIdInt = Integer.parseInt(recordId);
        String uid = resourcesDetail.getUid();
        UserResourceData dataResource = userResourceDao.selectUserResourceById(uid, recordIdInt);
        if (dataResource == null) {
            logger.error("user resource data not exist. uid={} recordIdInt={}", uid, recordIdInt);
            throw new CommonException(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }

        ResourceConfigData resourceConfigData = resourceConfigDao.getResourceDataFromDb(dataResource.getResourceId(), dataResource.getResourceType());
        if (null == resourceConfigData) {
            logger.error("not find resource data. recordIdInt={}", recordIdInt);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }

        userResourceDao.updateStatusById(uid, recordIdInt, 1);
        itemChangesReport(uid, 2, resourceConfigData.getResourceId(), resourceConfigData.getName(), dataResource.getResourceNumber(), 0, 1, 1);
        return ApiResult.getOk();
    }

    /**
     * 用户获取资源并设置
     */
    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        return add(resourcesDetail);
    }

    /**
     * 移除用户资源
     */
    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        String recordId = resourcesDetail.getRecordId();
        if (ObjectUtils.isEmpty(recordId)){
            logger.error("VipCardResourcesHandler recordId is empty resourcesDetail:{}", JSONObject.toJSONString(resourcesDetail));
            return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
        }
        int recordIdInt = Integer.parseInt(recordId);
        String uid = resourcesDetail.getUid();
        UserResourceData dataResource = userResourceDao.selectUserResourceById(uid, recordIdInt);
        if (dataResource == null) {
            logger.error("user resource data not exist. uid={} recordIdInt={}", uid, recordIdInt);
            throw new CommonException(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        userResourceDao.deleteUserResourceById(uid, recordIdInt);
        itemChangesReport(uid, 2, dataResource.getResourceId(), "", dataResource.getResourceNumber(), 0, 3, 1);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<UserResourceData> expiredResourceList = userResourceDao.getVipCardExpiredResourceList(BaseDataResourcesConstant.TYPE_VIP_CARD, DataResourcesConstant.BASE_EXIPRE_START_TIME, now);
        int size = CollectionUtils.isEmpty(expiredResourceList) ? 0 : expiredResourceList.size();
        logger.info("expiredResourceList. size={}", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(BaseDataResourcesConstant.TYPE_VIP_CARD);
        resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
        int expireCount = 0;
        for (UserResourceData item : expiredResourceList) {
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(item.getResourceId()));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            resourcesDetail.setEndTime((int) item.getEndTime());
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            userResourceDao.updateStatusById(item.getUid(), item.getId(), 2);
            itemChangesReport(item.getUid(), 2, item.getResourceId(), "", item.getResourceNumber(), 0, 2, 1);
            this.writeToDb(resourcesDetail);
            expireCount++;
            logger.info("resourceData expire success. resourcesDetail={}", resourcesDetail.toString());
        }
        logger.info("expiredResourceList size={} expireCount={}", size, expireCount);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        int now = DateHelper.getNowSeconds();
        List<UserResourceData> allExpireData = userResourceDao.getVipCardExpiredResourceList(BaseDataResourcesConstant.TYPE_VIP_CARD, now, now + 86400);
        int size = CollectionUtils.isEmpty(allExpireData) ? 0 : allExpireData.size();
        if(size > 0){
            Map<String, List<UserResourceData>> userResourceGroupData = allExpireData.stream().collect(Collectors.groupingBy(UserResourceData::getUid));
            userResourceGroupData.forEach((uid, userResourceDataList) -> {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                int slang = actorData.getSlang();
                String actText = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.ACTION_AR : ResourceOfficialMsgConstant.ACTION_EN;
                String title = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.VIP_CARD_EXPIRE_AR_EXPIRE : ResourceOfficialMsgConstant.VIP_CARD_EXPIRE_EN_EXPIRE;
                String body = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.VIP_CARD_EXPIRE_DESC_EXPIRE_AR : ResourceOfficialMsgConstant.VIP_CARD_EXPIRE_DESC_EXPIRE_EN;
                commonOfficialMsg(uid, "", 0, actText, title, body, VIP_RECORD_URL);
            });
        }
        return ApiResult.getOk();
    }

    @Override
    public void sendOfficialMsg(ResourcesDetail resourcesDTO) {
        String uid = resourcesDTO.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (AppVersionUtils.versionCheck(864, actorData.getVersion_code(), actorData.getIntOs())) {
            int resId = Integer.parseInt(resourcesDTO.getResId());
            ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromCache(resId, resourcesDTO.getResType());
            if (resourceData == null){
                return;
            }
            List<ResourceConfigData> resConfigDataList = resourceConfigDao.getResourceAllListFromDb(BaseDataResourcesConstant.TYPE_VIP_CARD);
            Map<Integer, ResourceConfigData> vipLevelResConfigDataMap = resConfigDataList.stream().collect(Collectors.toMap(ResourceConfigData::getRedundantField, Function.identity()));
            int gender = actorData.getFb_gender();
            int resVipLevel = resourceData.getRedundantField();
            if (gender == 2 && (resVipLevel == VipFeatureConstant.VIP_LEVEL_5 || resVipLevel == VipFeatureConstant.VIP_LEVEL_6)) {
                // 女性不可拥有VIP5、VIP6卡，将其升级为Queen VIP卡
                resourceData = vipLevelResConfigDataMap.get(VipFeatureConstant.VIP_LEVEL_QUEEN);
                if (resourceData == null) {
                    return;
                }
            }

            if (gender == 1 && resVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
                resourceData = vipLevelResConfigDataMap.get(VipFeatureConstant.VIP_LEVEL_5);
                if (resourceData == null) {
                    return;
                }
            }

            int slang = actorData.getSlang();
            String actText = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.ACTION_AR : ResourceOfficialMsgConstant.ACTION_EN;
            String title = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.VIP_CARD_GOT_AR_EXPIRE : ResourceOfficialMsgConstant.VIP_CARD_GOT_EN_EXPIRE;
            String body = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.VIP_CARD_GOT_DESC_EXPIRE_AR : ResourceOfficialMsgConstant.VIP_CARD_GOT_DESC_EXPIRE_EN;
            String vipName = slang == SLangType.ARABIC ? resourceData.getNameAr() : resourceData.getName();
            body = String.format(body, vipName, resourcesDTO.getDays());
            commonOfficialMsg(uid, resourceData.getIcon(), 0, actText, title, body, VIP_RECORD_URL);
        }
    }

    private void sendVipCardNumMsg(String uid, int num) {
        long vipCardCount = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.VIP_CARD_COUNT, 0);
        vipCardCount += num;
        actorConfigDao.updateUserConfig(uid, ActorConfigDao.VIP_CARD_COUNT, vipCardCount);
        VipCardNumMsg msg = new VipCardNumMsg();
        msg.setCardNum((int) vipCardCount);
        logger.info("sendVipCardNumMsg uid: {}, msg:{}", uid, JSON.toJSONString(msg));
        roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
    }

    public void itemChangesReport(String uid, int changeAction, int itemId, String itemName, int itemServiceLife, int itemAddType, int itemReduceType, int changeNums) {
        logger.info("itemChangesReport start");
        try {
            int currentTime = DateHelper.getNowSeconds();
            ItemsChangeEvent event = new ItemsChangeEvent();
            event.setUid(uid);
            event.setChange_action(changeAction);
            event.setItems_id(itemId);
            event.setItems_type(BaseDataResourcesConstant.TYPE_VIP_CARD);
            event.setItems_name(itemName);
            event.setItems_service_life(itemServiceLife);
            event.setItems_add_type(itemAddType);
            event.setItems_reduce_type(itemReduceType);
            event.setChange_nums(changeNums);
            event.setCtime(currentTime);
            eventReport.track(new EventDTO(event));
            logger.info("itemChangesReport success, event: {}", JSON.toJSONString(event));
        } catch (Exception e) {
            logger.error("itemChangesReport error: {}", e.getMessage(), e);
        }
    }

}
