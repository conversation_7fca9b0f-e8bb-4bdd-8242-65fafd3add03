package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.FcmPushEvent;
import com.quhong.constant.FcmMsgTypeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.SendFcmDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.LogType;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.FriendsData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.redis.FcmMsgRedis;
import com.quhong.redis.NewRookieRoomRedis;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.utils.ActorUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class UserFcmPushHandler implements MqMessageHandler{

    private static final Logger logger = LoggerFactory.getLogger(UserFcmPushHandler.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private static final Integer FEMALE_ON_MIC_TIME = 20;
    private static final List<String> SAME_COUNTRY_PUSH_EN = Arrays.asList("Interesting people are waiting for you, come and join the hot chat room!", "Whose story is more exciting today? Click in and listen!", "Your friend has just joined the voice room, come and interact!",
                    "Lonely night? Come to the room to chat, warmth is by your ears.", "Tell us about your story, or listen to other people's world.", "There are surprises in this room, let's discover them together!", "I heard there are hilarious jokes here today, come and watch!",
                    "Come to the voice room, you may meet interesting souls!", "The friend you follow is online, click in to interact together!", "The evening breeze is blowing, the room is full of laughter, click to enter.", "Come to the voice room and chat with old friends about new topics!",
                    "Click in and feel the warm voice will bring healing.", "The room is open, waiting for you to lead today's topic!", "New topics, new voices, all you need is to join!", "Don't want to browse your phone alone? There are people waiting for you to chat here!",
                    "Good voice relay, come and experience the real voice show!", "The voice room is very lively, your friends are waiting for you!");
    private static final List<String> SAME_COUNTRY_PUSH_AR = Arrays.asList("ينتظرك أشخاص مثيرون للاهتمام، تعالوا وانضموا إلى غرفة الدردشة الساخنة!", "من هي قصتك الأكثر إثارة اليوم؟ انقر واستمع!", "لقد انضم صديقك للتو إلى غرفة الصوت، تعالوا وتفاعلوا!",
            "ليلة وحيدة؟ تعالوا إلى الغرفة للدردشة، الدفء عند أذنيك.", "أخبرونا عن قصتك، أو استمعوا إلى عالم الآخرين.", "هناك مفاجآت في هذه الغرفة، فلنكتشفها معًا!", "سمعت أن هناك نكاتًا مضحكة هنا اليوم، تعال وشاهد!", "تعال إلى غرفة الصوت، فقد تقابل أرواحًا مثيرة للاهتمام!"
            , "الصديق الذي تتابعه اونلاين، انقر للتفاعل معًا!", "تهب نسيم المساء، الغرفة مليئة بالضحك، انقر للدخول.", "تعال إلى غرفة الصوت وتحدث مع الأصدقاء القدامى حول مواضيع جديدة!", "انقر واشعر بالصوت الدافئ الذي سيجلب الشفاء.",
            "الغرفة مفتوحة، في انتظارك لتقود موضوع اليوم!", "مواضيع جديدة، أصوات جديدة، كل ما تحتاجه هو الانضمام!", "لا تريد تصفح هاتفك بمفردك؟ هناك أشخاص ينتظرونك للدردشة هنا!", "نقل صوتي جيد، تعال واستمتع بعرض صوتي حقيقي!", "غرفة الصوت حيوية للغاية، أصدقاؤك ينتظرونك!");

    @Resource
    private FcmMsgRedis fcmMsgRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private DAUDao dauDao;
    @Resource
    private PlayerStatusRedis playerStatusRedis;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private EventReport eventReport;

    @Override
    public void process(CommonMqTopicData mqData) {
        if (mqData == null || StringUtils.isEmpty(mqData.getUid()) || StringUtils.isEmpty(mqData.getRoomId())) {
            return;
        }
        String uid = mqData.getUid();
        String roomId = mqData.getRoomId();
        String item = mqData.getItem();

        if (CommonMqTaskConstant.ON_MIC_TIME.equals(item)) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            newFriendOnMicPush(uid, roomId, actorData, roomData);
            // femaleOnMicPush(uid, roomId, actorData, roomData);
        }
    }

    private void newFriendOnMicPush(String uid, String roomId, ActorData actorData, MongoRoomData roomData) {
        try {
            int pushFriendUser = fcmMsgRedis.getPushFriendUserStatus(uid);
            if (roomData == null){
                return;
            }
            if(pushFriendUser <= 0){
                long millis = System.currentTimeMillis();
                List<FriendsData> friendsDataList = friendsDao.findAllFriend(uid, false);
                int currentTime = DateHelper.getNowSeconds();
                int lastSevenTime = currentTime - 7 * 86400;
                List<String> friendUidList = friendsDataList.stream().filter(item -> item.getCtime() >= lastSevenTime).map(friend -> uid.equals(friend.getUidSecond()) ? friend.getUidFirst() : friend.getUidSecond()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(friendUidList)){
                    fcmMsgRedis.setPushFriendUserStatus(uid, 4*3600);
                    SendFcmDTO sendFcmDTO = new SendFcmDTO();
                    Map<String, String> paramMap = new HashMap<>();
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("roomId", roomId);
                    paramMap.put("t", FcmMsgTypeConstant.LIVE_ROOM);
                    paramMap.put("custom", JSONObject.toJSONString(jsonObject));
                    paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, new ObjectId().toString());
                    paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
                    paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_4);
                    paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, roomData.getName());
                    paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
                    JSONObject jsonObject2 = new JSONObject();
                    jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
                    paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));

                    sendFcmDTO.setParamMap(paramMap);
                    sendFcmDTO.setToUidSet(new HashSet<>(friendUidList));
                    sendFcmDTO.setTitle(roomData.getName());
                    sendFcmDTO.setTitleAr(roomData.getName());
                    sendFcmDTO.setBody(String.format("Your friend %s is online!", actorData.getName()));
                    sendFcmDTO.setBodyAr(String.format("صديقك %s اونلاين!", actorData.getName()));
                    sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                    iMsgService.sendFcmMsg(sendFcmDTO);
                    msgLogger.info("newFriendOnMicPush timeMillis={}",  System.currentTimeMillis() - millis);
                }
            }
        }catch (Exception e){
            logger.error("newFriendOnMicPush error:{}", e.getMessage(), e);
        }
    }
    private void femaleOnMicPush(String uid, String roomId,  ActorData actorData, MongoRoomData roomData) {
        try {
            if(ActorUtils.getRegDays(uid) <= 30 && actorData.getFb_gender() == 2 && !newRookieRoomRedis.isHangUpRoom(roomId)){
                long millis = System.currentTimeMillis();
                int afterOnMicTime = fcmMsgRedis.incFemaleOnMicTime(uid, 1);
                if(afterOnMicTime % FEMALE_ON_MIC_TIME != 0){
                    return;
                }

                Set<String> activeUserSet = dauDao.getActiveUserSet(2);
                List<String> activeUserList = new ArrayList<>(activeUserSet);
                Collections.shuffle(activeUserList);
                List<String> pushUserList = activeUserList.size() > 1000 ? activeUserList.subList(0, 2000) : activeUserList;

                Set<String> pushTokenSet = new HashSet<>();
                String uidCountry = actorData.getCountry();
                String fcmMessageId = new ObjectId().toString();
                for (String aid : pushUserList) {
                    ActorData aidActorData = actorDao.getActorDataFromCache(aid);
                    if(!aidActorData.getCountry().equals(uidCountry)){
                        continue;
                    }
                    if(aidActorData.getRobot() > 0){
                        continue;
                    }

                    if(playerStatusRedis.getPlayerStatus(aid) > 0){
                        continue;
                    }

                    if (null == aidActorData.getPush() ){
                        continue;
                    }

                    if(aidActorData.getPush().getToken() == null){
                        continue;
                    }

                    if(fcmMsgRedis.getPushMaleSameCountryUserStatus(aid) > 0){
                        continue;
                    }

                    String pushToken = aidActorData.getPush().getToken();
                    fcmMsgRedis.setPushMaleSameCountryUserStatus(aid, 8*3600);
                    pushTokenSet.add(pushToken);

                    doFcmReportEvent(fcmMessageId, aid, FcmMsgTypeConstant.FCM_SYSTEM, roomData.getName(), FcmMsgTypeConstant.FCM_SUB_5);
                }

                if (CollectionUtils.isEmpty(pushTokenSet)){
                    return;
                }

                SendFcmDTO sendFcmDTO = new SendFcmDTO();
                Map<String, String> paramMap = new HashMap<>();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("roomId", roomId);
                paramMap.put("t", FcmMsgTypeConstant.LIVE_ROOM);
                paramMap.put("custom", JSONObject.toJSONString(jsonObject));


                paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, fcmMessageId);
                paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
                paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_5);
                paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, roomData.getName());
                paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
                JSONObject jsonObject2 = new JSONObject();
                jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
                paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));

                sendFcmDTO.setParamMap(paramMap);
                sendFcmDTO.setToTokenSet(pushTokenSet);
                sendFcmDTO.setTitle(roomData.getName());
                sendFcmDTO.setTitleAr(roomData.getName());
                sendFcmDTO.setBody(SAME_COUNTRY_PUSH_EN.get(new Random().nextInt(SAME_COUNTRY_PUSH_EN.size())));
                sendFcmDTO.setBodyAr(SAME_COUNTRY_PUSH_AR.get(new Random().nextInt(SAME_COUNTRY_PUSH_AR.size())));
                sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                iMsgService.sendFcmMsgByToken(sendFcmDTO);
                msgLogger.info("femaleOnMicPush timeMillis={}",  System.currentTimeMillis() - millis);
            }
        }catch (Exception e){
            logger.error("femaleOnMicPush error:{}", e.getMessage(), e);
        }
    }


    private void doFcmReportEvent(String fcmMessageId, String uid, String fcmType, String fcmTitle, String fcmSubType) {
        FcmPushEvent event = new FcmPushEvent();
        event.setCtime(DateHelper.getNowSeconds());
        event.setUid(uid);
        event.setFcm_message_id(fcmMessageId);
        event.setFcm_type(fcmType);
        event.setFcm_title(fcmTitle);
        event.setFcm_subtype(fcmSubType);
        event.setFcm_push_status(1);
        event.setFcm_action(1001);
        eventReport.track(new EventDTO(event));
    }

}
