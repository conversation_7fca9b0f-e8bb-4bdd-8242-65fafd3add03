//
//package com.quhong.handler;
//
//import com.quhong.config.CaffeineCacheConfig;
//import com.quhong.data.GiftContext;
//import com.quhong.data.dto.SendGiftDTO;
//import com.quhong.mongo.dao.SysConfigDao;
//import com.quhong.mysql.data.GiftData;
//import com.quhong.redis.WeeklyStarRedis;
//import com.quhong.room.TestRoomService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.cache.annotation.Cacheable;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * weeklyStar
// */
//@Component
//public class WeeklyStarHandler implements IGiftHandler {
//    protected static final Logger logger = LoggerFactory.getLogger(WeeklyStarHandler.class);
//
//    @Resource
//    private TestRoomService testRoomService;
//    @Resource
//    private SysConfigDao configDao;
//    @Resource
//    private WeeklyStarRedis weeklyStarRedis;
//    @Resource
//    private WeeklyStarHandler weeklyStarHandler;
//
//    @Override
//    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
////        if (testRoomService.isTestRoom(req.getRoomId())) {
////            return;
////        }
////        if (!weeklyStarHandler.getWeeklyGiftList().contains(giftData.getRid())) {
////            return;
////        }
////        if (!weeklyStarHandler.getHideUidList().contains(req.getUid())) {
////        }
////        int weekCount = weeklyStarHandler.getWeekCount();
////        weeklyStarRedis.incrWeeklyStarScore(req.getUid(), giftData.getRid(), weekCount, req.getNumber() * context.getAidSet().size());
//
//    }
//
//    @Cacheable(value = "getWeeklyGiftList", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
//    public List<Integer> getWeeklyGiftList() {
//        return configDao.getList(SysConfigDao.NEW_GIFT_KEY, SysConfigDao.NEW_GIFT_LIST_KEY, false);
//    }
//
//    @Cacheable(value = "getWeekCount", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
//    public int getWeekCount() {
//        return configDao.getIntValue(SysConfigDao.WEEK_COUNT, SysConfigDao.WEEK_COUNT, false);
//    }
//
//    @Cacheable(value = "getHideUidList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
//    public List<String> getHideUidList() {
//        return configDao.getList(SysConfigDao.HIDE_UID_LIST, SysConfigDao.HIDE_UID_LIST);
//    }
//}
