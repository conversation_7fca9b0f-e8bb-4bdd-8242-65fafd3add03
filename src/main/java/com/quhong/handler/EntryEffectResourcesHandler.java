package com.quhong.handler;

import com.quhong.cache.CacheMap;
import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.constant.ResourceOfficialMsgConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CheckExpireData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ResourceConfigDao;
import com.quhong.mongo.data.ResourceConfigData;
import com.quhong.mysql.dao.UserResourceDao;
import com.quhong.mysql.data.ResourcesDetail;
import com.quhong.mysql.data.UserResourceData;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Component
public class EntryEffectResourcesHandler extends BaseResourcesHandler {

    private static final Logger logger = LoggerFactory.getLogger(EntryEffectResourcesHandler.class);

    private final CacheMap<String, CheckExpireData> cacheMap;

    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private ActorDao actorDao;

    public EntryEffectResourcesHandler() {
        cacheMap = new CacheMap<>(EXPIRE_TIME_DEFAULT);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    /**
     * 用户活得资源
     */
    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        ResourceConfigData data = resourceConfigDao.getResourceDataFromDb(resId, resourcesDetail.getResType());
        if (null == data) {
            logger.error("not find resource data. resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        resourcesDetail.setName(data.getName());
        if (!updateResource(resourcesDetail, false)) {
            logger.info("use not match. uid={} resId={} gainType={} itemType={}", resourcesDetail.getUid(), resId, resourcesDetail.getGainType(), data.getItemType());
            return ApiResult.getError(DataResourcesHttpCode.RESOURCE_NOT_MATCH_USE);
        }
        return ApiResult.getOk();
    }

    /**
     * 用户设置资源
     */
    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        String uid = resourcesDetail.getUid();
        UserResourceData dataResource = userResourceDao.selectUserResource(uid, resId);
        if (dataResource == null) {
            logger.error("user resource data not exist. uid={} resId={}", uid, resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
        }
        UserResourceData curUsingResource = userResourceDao.selectUserUsingResource(uid, resourcesDetail.getResType());
        if (curUsingResource != null) {
            if (dataResource.getResourceId() == curUsingResource.getResourceId()) {
                return ApiResult.getOk();
            }
            userResourceDao.updateStatus(uid, curUsingResource.getResourceId(), 0);
        }
        userResourceDao.updateStatus(uid, resId, 1);
        return ApiResult.getOk();
    }

    /**
     * 用户获取资源并设置
     */
    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        ResourceConfigData data = resourceConfigDao.getResourceDataFromDb(resId, resourcesDetail.getResType());
        if (data == null) {
            logger.info("can not find resource config data. resId={}", resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_FIND_RESOURCES);
        }
        resourcesDetail.setName(data.getName());
        int itemType = data.getItemType();
        int gainType = resourcesDetail.getGainType();
        if (!updateResource(resourcesDetail, true)) {
            logger.info("use not match. uid={} resId={} gainType={} itemType={}", resourcesDetail.getUid(), resId, gainType, itemType);
            return ApiResult.getError(new HttpCode(1, ""));
        }
        return ApiResult.getOk();
    }

    /**
     * 用户取消设置资源
     */
    @Override
    public ApiResult<String> unWear(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        String uid = resourcesDetail.getUid();
        UserResourceData wearingResource = userResourceDao.selectUserResource(uid, resId);
        if (wearingResource == null) {
            logger.error("user resource data not exist. uid={} resId={}", uid, resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
        }
        userResourceDao.updateStatus(uid, resId, 0);
        return ApiResult.getOk();
    }

    /**
     * 移除用户资源
     */
    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        String uid = resourcesDetail.getUid();
        UserResourceData wearingResource = userResourceDao.selectUserResource(uid, resId);
        if (wearingResource == null) {
            logger.error("user resource data not exist. uid={} resId={}", uid, resId);
            return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
        }
        userResourceDao.deleteUserResource(uid, resId);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<UserResourceData> expiredResourceList = userResourceDao.getExpiredResourceList(BaseDataResourcesConstant.TYPE_ENTRY_EFFECT, DataResourcesConstant.BASE_EXIPRE_START_TIME, now);
        int size = CollectionUtils.isEmpty(expiredResourceList) ? 0 : expiredResourceList.size();
        logger.info("expiredResourceList. size={}", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(BaseDataResourcesConstant.TYPE_ENTRY_EFFECT);
        resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
        int expireCount = 0;
        for (UserResourceData item : expiredResourceList) {
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(item.getResourceId()));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            resourcesDetail.setEndTime((int) item.getEndTime());
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            userResourceDao.deleteUserResource(item.getUid(), item.getResourceId());
            this.writeToDb(resourcesDetail);
            expireCount++;
            logger.info("resourceData expire success. resourcesDetail={}", resourcesDetail.toString());
        }
        logger.info("expiredResourceList size={} expireCount={}", size, expireCount);
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }

    @Override
    public void sendOfficialMsg(ResourcesDetail resourcesDTO) {
        if (resourcesDTO.getOfficialMsg() > 0) {
            String uid = resourcesDTO.getUid();
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (AppVersionUtils.versionCheck(843, actorData.getVersion_code(), actorData.getIntOs())) {
                int resId = Integer.parseInt(resourcesDTO.getResId());
                ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromCache(resId, resourcesDTO.getResType());
                if (resourceData != null) {
                    int slang = actorData.getSlang();
                    String actText = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.ACTION_AR : ResourceOfficialMsgConstant.ACTION_EN;
                    String title = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.ENTRY_EFFECT_AR : ResourceOfficialMsgConstant.ENTRY_EFFECT_EN;
                    String body = slang == SLangType.ARABIC ? ResourceOfficialMsgConstant.ENTRY_EFFECT_DESC_AR : ResourceOfficialMsgConstant.ENTRY_EFFECT_DESC_EN;
                    commonOfficialMsg(uid, resourceData.getIcon(), ResourceOfficialMsgConstant.ACTION_TYPE_ENTRY_EFFECT, actText, title, body);
                }
            }
        }
    }

    public void checkUseExpire(String uid) {
        try {
            CheckExpireData data = cacheMap.getData(uid);
            String today = DateHelper.DEFAULT.formatDateInDay();
            int now = DateHelper.getNowSeconds();
            boolean useCache = cacheIsValid(data, today, now);
            if (!useCache) {
                data = new CheckExpireData();
                data.setUid(uid);
                data.setCheckDay(today);
                data.setCheckTime(now);
                data.setStatus(NONE_UPDATE_CHECK);
                List<UserResourceData> dataList = userResourceDao.selectUserUsingAllResourceList(uid);
                if (CollectionUtils.isEmpty(dataList)) {
                    return;
                }
                for (UserResourceData userResourceData : dataList) {
                    ResourcesDetail resourcesDetail = new ResourcesDetail();
                    resourcesDetail.setResType(userResourceData.getResourceType());
                    resourcesDetail.setActionType(DataResourcesConstant.ACTION_REDUCE);
                    resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_USE_REDUCE_DESC);
                    resourcesDetail.setGainType(DataResourcesConstant.GAIN_TYPE_USE);
                    resourcesDetail.setUid(uid);
                    resourcesDetail.setResId(String.valueOf(userResourceData.getResourceId()));
                    resourcesDetail.setmTime(now);
                    resourcesDetail.setHandleTime(now);
                    resourcesDetail.setEndTime((int) userResourceData.getEndTime());
                    if (userResourceData.getEndTime() <= now) {
                        userResourceDao.deleteUserResource(uid, userResourceData.getResourceId());
                        resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
                        resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
                    }
                    this.writeToDb(resourcesDetail);
                }
                cacheMap.cacheData(uid, data);
            }
        } catch (Exception e) {
            logger.error("checkUseExpire error. {}", e.getMessage(), e);
        }

    }

    private boolean updateResource(ResourcesDetail resourcesDetail, boolean putOn) {
        String uid = resourcesDetail.getUid();
        int resId = Integer.parseInt(resourcesDetail.getResId());
        int days = resourcesDetail.getDays();
        Integer seconds = resourcesDetail.getSeconds();
        int preResId;
        int emptyWearType = resourcesDetail.getEmptyWearType();
        boolean isEmptyWear = emptyWearType > 0;
        if (putOn || isEmptyWear) {
            UserResourceData oldResourceData = userResourceDao.selectUserUsingResource(uid, resourcesDetail.getResType());
            if (null != oldResourceData) {
                preResId = oldResourceData.getResourceId();
                if (putOn && resId != preResId) {
                    //取消佩戴
                    userResourceDao.updateStatus(oldResourceData.getUid(), preResId, 0);
                }
            } else {
                if (isEmptyWear) {
                    putOn = true;
                }
            }
        }
        UserResourceData data = userResourceDao.selectUserResource(uid, resId);
        if (data == null) {
            long endTime = DateHelper.getNowSeconds() + days * 86400L;
            insertUserResourceData(uid, resId, resourcesDetail.getResType(), endTime , putOn);
        } else {
            int status = putOn ? 1 : data.getStatus();
            long newEndTime = this.getResExtendTime(data.getEndTime(), days, seconds);
            userResourceDao.updateEndTime(uid, resId, status, newEndTime);
        }
        return true;
    }

    private void insertUserResourceData(String uid, int resId, int resType, long endTime, boolean putOn) {
        UserResourceData data = new UserResourceData();
        data.setUid(uid);
        data.setResourceId(resId);
        data.setResourceType(resType);
        data.setStatus(putOn ? 1 : 0);
        data.setEndTime(endTime);
        data.setCtime(DateHelper.getNowSeconds());
        userResourceDao.insert(data);
    }
}
