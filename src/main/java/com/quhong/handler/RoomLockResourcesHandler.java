package com.quhong.handler;

import com.quhong.constant.DataResourcesConstant;
import com.quhong.constant.DataResourcesHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiResult;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomLockDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RoomLockData;
import com.quhong.mysql.data.ResourcesDetail;
import com.quhong.redis.RoomPwdRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
public class RoomLockResourcesHandler extends BaseResourcesHandler {
    private static final Logger logger = LoggerFactory.getLogger(RoomLockResourcesHandler.class);

    @Resource
    private RoomLockDao roomLockDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RoomPwdRedis roomPwdRedis;

    @Override
    public ApiResult<String> add(ResourcesDetail resourcesDetail) {
        int resId = Integer.parseInt(resourcesDetail.getResId());
        int days = resourcesDetail.getDays();
        Integer seconds = resourcesDetail.getSeconds();
        RoomLockData roomLockData = roomLockDao.findData(resourcesDetail.getUid());
        long endTime = resourcesDetail.getEndTime();
        if (null != roomLockData) {
            endTime = this.getResExtendTime(roomLockData.getEnd_time(), days, seconds);
            resourcesDetail.setEndTime((int) endTime);
            roomLockDao.updateRoomLockByTime(resourcesDetail.getUid(), endTime, roomLockData);
        } else {
            roomLockDao.insertDB(resourcesDetail.getUid(), (int) endTime);
        }
        return ApiResult.getOk();

    }

    @Override
    public ApiResult<String> wear(ResourcesDetail resourcesDetail) {
        logger.info("room lock can not wear resourcesDetail:{}", resourcesDetail);
        return ApiResult.getError(DataResourcesHttpCode.PARAMS_ERROR);
    }

    @Override
    public ApiResult<String> remove(ResourcesDetail resourcesDetail) {
        RoomLockData roomLockData = roomLockDao.findData(resourcesDetail.getUid());
        if (null != roomLockData) {
            int setEndTime = resourcesDetail.getSetEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (setEndTime > 1 && (setEndTime - currentTime > 0)) {
                int leftTime = setEndTime - currentTime;
                long newEndTime = roomLockData.getEnd_time() - leftTime;
                roomLockDao.updateRoomLockByTime(resourcesDetail.getUid(), newEndTime, roomLockData);
                return ApiResult.getOk();
            }
            roomLockDao.removeRoomLock(roomLockData);
            reSetRoomPwd(resourcesDetail.getUid());
            return ApiResult.getOk();
        }
        return ApiResult.getError(DataResourcesHttpCode.NOT_OWN_RESOURCES);
    }

    @Override
    public ApiResult<String> addAndWear(ResourcesDetail resourcesDetail) {
        return add(resourcesDetail);
    }

    @Override
    public ApiResult<String> expire() {
        int now = DateHelper.getNowSeconds();
        List<RoomLockData> allExpireData = roomLockDao.listByEndTime(DataResourcesConstant.BASE_EXIPRE_START_TIME, now);
        int size = CollectionUtils.isEmpty(allExpireData) ? 0 : allExpireData.size();
        logger.info("RoomLockData allExpireData size={} ", size);
        ResourcesDetail resourcesDetail = new ResourcesDetail();
        resourcesDetail.setResType(DataResourcesConstant.TYPE_ROOM_LOCK);
        for (RoomLockData item : allExpireData) {
            resourcesDetail.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesDetail.setUid(item.getUid());
            resourcesDetail.setResId(String.valueOf(DataResourcesConstant.DEFAULT_ROOM_LOCK_ID));
            resourcesDetail.setmTime(now);
            resourcesDetail.setHandleTime(now);
            long nowEtime = item.getEnd_time();
            resourcesDetail.setEndTime((int) nowEtime);
            resourcesDetail.setDesc(DataResourcesConstant.EXIPRE_DESC);
            long eTime = this.getVipEndTime(item.getUid(), DataResourcesConstant.MIN_VIP_GET_ROOM_LOCK,now);
            if (eTime >= now + 30) {
                roomLockDao.updateRoomLockByTime(resourcesDetail.getUid(), eTime, item);
                resourcesDetail.setActionType(DataResourcesConstant.ACTION_EXPIRE_GET);
                resourcesDetail.setEndTime((int) eTime);
                logger.info("action_expire_get eTime={} now={} nowEtime={} resourcesDetail={}", eTime, now, nowEtime, resourcesDetail);
            } else {
                roomLockDao.removeRoomLock(item);
                reSetRoomPwd(item.getUid());
            }
            this.writeToDb(resourcesDetail);
            logger.info("room lock exipre success resourcesDetail={}", resourcesDetail);
        }
        return ApiResult.getOk();
    }

    @Override
    public ApiResult<String> expireNotified() {
        return null;
    }


    private void reSetRoomPwd(String uid) {
        String roomId = RoomUtils.formatRoomId(uid);
        MongoRoomData mongoRoomData = mongoRoomDao.findData(roomId);
        if (mongoRoomData != null) {
            mongoRoomDao.updateField(roomId, "pwd", "");
            roomPwdRedis.remove(roomId);
            logger.info("room pwd set empty success uid={}", uid);
        } else {
            logger.info("room lock delete fail not find mongo room uid={}", uid);
        }
    }

}
