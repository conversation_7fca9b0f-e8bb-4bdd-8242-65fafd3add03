package com.quhong.controllers;

import com.quhong.data.dto.BubbleLoadDTO;
import com.quhong.data.dto.VipBuyDTO;
import com.quhong.data.dto.VipDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.VipService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("${baseUrl}")
public class VipController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(VipController.class);

    @Resource
    private VipService vipService;


    @RequestMapping(value = "check")
    private String vipCheck(HttpServletRequest request) {
        VipDTO dto = RequestUtils.getSendData(request, VipDTO.class);
        logger.info("showpage requestId={} uid={}", dto.getRequestId(), dto.getUid());
        return createResult(dto, HttpCode.SUCCESS, null);
    }

    /**
     * 查询当前vip信息
     */
    @RequestMapping(value = "showpage")
    private String vipShowPage(HttpServletRequest request) {
        VipDTO dto = RequestUtils.getSendData(request, VipDTO.class);
        logger.info("show page requestId={} uid={}", dto.getRequestId(), dto.getUid());
        VipShowPageVO vo = vipService.getVipShowPage(dto);
        logger.info("VipShowPageVO: {}", vo);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }


    /**
     * 购买vip
     */
    @RequestMapping(value = "buy")
    private String vipBuy(HttpServletRequest request) {
        VipBuyDTO dto = RequestUtils.getSendData(request, VipBuyDTO.class);
        logger.info("vipBuy requestId={} uid={}", dto.getRequestId(), dto.getUid());
        VipBuyVO vo = vipService.buyVip(dto);
        logger.info("vipBuy: {}", vo);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }

    /**
     * 查询女王vip信息
     */
    @RequestMapping(value = "queen_showpage")
    private String vipQueenShowPage(HttpServletRequest request) {
        VipDTO dto = RequestUtils.getSendData(request, VipDTO.class);
        logger.info("show page requestId={} uid={}", dto.getRequestId(), dto.getUid());
        VipQueenShowVO vo = vipService.getQueenVipShowPage(dto);
        logger.info("VipQueenShowVO: {}", vo);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }

    /**
     * 购买vip
     */
    @RequestMapping(value = "buy_queen")
    private String vipBuyQueen(HttpServletRequest request) {
        VipDTO dto = RequestUtils.getSendData(request, VipDTO.class);
        logger.info("vipBuyQueen requestId={} uid={}", dto.getRequestId(), dto.getUid());
        VipBuyVO vo = vipService.vipBuyQueen(dto);
        logger.info("vipBuyQueen: {}", vo);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }



    /**
     * 入场动画下载链接
     */

    @RequestMapping(value = "resource_download")
    private String joinResourceDownload(HttpServletRequest request) {
        VipDTO dto = RequestUtils.getSendData(request, VipDTO.class);
        // logger.info("resourceDownload requestId={} uid={}", dto.getRequestId(), dto.getUid());
        JoinResourceVO vo = vipService.joinResourceDownload(dto);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }


    /**
     * 气泡下载链接
     */

    @RequestMapping(value = "buddle_resource_download")
    private String bubbleDownload(HttpServletRequest request) {
        BubbleLoadDTO dto = RequestUtils.getSendData(request, BubbleLoadDTO.class);
        // logger.info("bubbleDownload requestId={} uid={}", dto.getRequestId(), dto.getUid());
        BubbleVO vo = vipService.bubbleDownload(dto);
        return createResult(dto, HttpCode.SUCCESS, vo);
    }


}
