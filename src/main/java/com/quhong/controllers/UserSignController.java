package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.dto.SignCheckV9DTO;
import com.quhong.data.dto.UserSignDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.redis.DataRedisBean;
import com.quhong.service.UserSignService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 *  用户签到
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class UserSignController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(UserSignController.class);

    @Resource
    private UserSignService userSignService;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * 新手体验信息
     */
    @RequestMapping("sign/rookie_info")
    public String getRookieSignInfo(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        RookieSignInfoVO vo = userSignService.getRookieSignInfo(req);
        logger.info("get rookie sign list. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 新签到版本，主动签到前查看用户该次签到能获得多少钻石
     */
    @RequestMapping("sign/check_sign_beans")
    public String checkSignBeans(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        CheckSignBeansVO vo = userSignService.checkSignBeans(req);
        logger.info("check sign beans. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 新签到
     */
    @RequestMapping("sign/sign8")
    public String sign7(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        req.setRookieBag(true);
        req.setIp(RequestUtils.getIpAddress(request));
        SignVO vo = userSignService.sign(req);
        logger.info("user sign. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 检查签到接口
     */
    @RequestMapping("sign/check_sign8_reward")
    public String checkSign8Reward(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        req.setRookieBag(true);
        CheckSignRewardVO vo = userSignService.checkSignReward(req);
        logger.info("check sign 8 reward. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 检查签到接口(签到送背包礼物版本)
     */
    @RequestMapping("sign/check_sign6_reward")
    public String checkSign6Reward(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        req.setRookieBag(false);
        CheckSignRewardVO vo = userSignService.checkSignReward(req);
        logger.info("check sign 6 reward. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 8.43新用户礼包（没有调用）
     */
    @RequestMapping("sign/rookie_list")
    public String getRookieListInfo(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        RookieBagReward843VO vo = userSignService.rookieBagReward843(req);
        logger.info("get rookie sign list. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     *
     * 8.43新用户礼包领取（没有调用）
     */
    @RequestMapping("sign/rookie_sign")
    public String rookieSign(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        RookieBagReward843VO vo = userSignService.rookieSign(req);
        logger.info("rookie sign uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 测试服触发签到弹窗
     */
    @RequestMapping("sign/test")
    public String signTest(HttpServletRequest request) {
        UserSignDTO req = RequestUtils.getSendData(request, UserSignDTO.class);
        if (ServerConfig.isProduct()) {
            return createResult(req, HttpCode.SUCCESS, null);
        }
        logger.info("sign test. uid={} ", req.getUid());
        try {
            clusterRedis.opsForValue().set("str:test:fakeday:" + req.getUid(), req.getFakeDay() + "");
        } catch (Exception e) {
            logger.error("add test fake day error. uid={} {}", e.getMessage(), e);
        }
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 新签到检查v9版本(8641版本)
     */
    @RequestMapping("signCheckV9")
    public HttpResult<SignCheckV9VO> signCheckV9(@RequestBody SignCheckV9DTO dto) {
        SignCheckV9VO vo = userSignService.signCheckV9(dto);
        logger.info("signCheckV9 dto:{}, vo:{}", JSONObject.toJSONString(dto), JSONObject.toJSONString(vo));
        return HttpResult.getOk(vo);
    }


}
