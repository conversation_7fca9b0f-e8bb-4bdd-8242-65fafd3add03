package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.quhong.data.dto.VipDTO;
import com.quhong.dto.VipV2BuyDTO;
import com.quhong.data.vo.VipCardListVO;
import com.quhong.data.vo.VipV2BuyVO;
import com.quhong.data.vo.VipV2InfoVO;
import com.quhong.data.vo.VipExpirePopupVO;
import com.quhong.data.vo.VipRecordVO;
import com.quhong.data.dto.VipCardActivateDTO;
import com.quhong.datas.HttpResult;
import com.quhong.service.VipV2Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Vip版本2
 * <AUTHOR>
 */
@RestController
@RequestMapping("/vip/")
public class VipV2Controller {

    private static final Logger logger = LoggerFactory.getLogger(VipV2Controller.class);

    @Resource
    private VipV2Service vipV2Service;

    /**
     * vip信息
     */
    @RequestMapping("vipInfo")
    public HttpResult<VipV2InfoVO> vipInfo(@RequestBody VipDTO dto) {
        logger.info("vipInfo dto: {}", JSON.toJSONString(dto));
        return HttpResult.getOk(vipV2Service.vipInfo(dto));
    }

    /**
     * 购买vip
     */
    @RequestMapping("vipBuy")
    public HttpResult<VipV2BuyVO> vipBuy(@RequestBody VipV2BuyDTO dto) {
        logger.info("vipBuy dto: {}", JSON.toJSONString(dto));
        VipV2BuyVO vo = vipV2Service.vipBuy(dto);
        logger.info("vipBuy result: {}", JSON.toJSONString(vo));
        return HttpResult.getOk(vo);
    }

    /**
     * VIP过期弹窗检查
     */
    @RequestMapping("vipExpirePopup")
    public HttpResult<VipExpirePopupVO> vipExpirePopup(@RequestBody VipDTO dto) {
        logger.info("vipExpirePopup dto: {}", JSON.toJSONString(dto));
        VipExpirePopupVO vo = vipV2Service.checkVipExpirePopup(dto);
        logger.info("vipExpirePopup result: popupType={}, vipLevel={}, leftDays={}", vo.getPopupType(), vo.getVipLevel(), vo.getLeftDays());
        return HttpResult.getOk(vo);
    }

    /**
     * vip自动佩戴资源
     */
    @RequestMapping("vipAutoWearResource")
    public HttpResult<?> vipAutoWearResource(@RequestBody VipDTO dto) {
        logger.info("vipAutoWearResource dto: {}", JSON.toJSONString(dto));
        vipV2Service.vipAutoWearResource(dto);
        return HttpResult.getOk();
    }

    /**
     * VIP卡列表
     */
    @RequestMapping("vipCardList")
    public HttpResult<VipCardListVO> vipCardList(@RequestBody VipDTO dto) {
        logger.info("vipCardList dto: {}", JSON.toJSONString(dto));
        VipCardListVO vo = vipV2Service.getVipCardList(dto);
        logger.info("vipCardList result size: {}", vo.getList() != null ? vo.getList().size() : 0);
        return HttpResult.getOk(vo);
    }

    /**
     * VIP记录查询
     */
    @RequestMapping("vipRecord")
    public HttpResult<VipRecordVO> vipRecord(@RequestBody VipDTO dto) {
        logger.info("vipRecord dto: {}", JSON.toJSONString(dto));
        int page = dto.getPage() != null ? dto.getPage() : 1;
        VipRecordVO vo = vipV2Service.vipRecord(dto.getUid(), page);
        logger.info("vipRecord result: uid={}, page={}, recordCount={}", dto.getUid(), page, vo.getList() != null ? vo.getList().size() : 0);
        return HttpResult.getOk(vo);
    }

    /**
     * VIP卡激活
     */
    @RequestMapping("vipCardActivate")
    public HttpResult<VipV2BuyVO> vipCardActivate(@RequestBody VipCardActivateDTO dto) {
        logger.info("vipCardActivate dto: {}", JSON.toJSONString(dto));
        VipV2BuyVO vo = vipV2Service.activateVipCard(dto);
        logger.info("vipCardActivate result: vipLevel={}, expireTime={}", vo.getVipLevel(), vo.getExpireTime());
        return HttpResult.getOk(vo);
    }

}
