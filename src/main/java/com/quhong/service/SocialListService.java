package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.RoomListConstant;
import com.quhong.constant.RoomListHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.PartyListData;
import com.quhong.data.SociaEntryData;
import com.quhong.data.bo.GamePlayStatusBO;
import com.quhong.data.dto.PartyListDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.ClientOS;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.mysql.data.UserLabelData;
import com.quhong.redis.*;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.redis.RoomRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SocialListService {
    private static final Logger logger = LoggerFactory.getLogger(SocialListService.class);
    private static final String ALL_COUNTRY = "ALL";

    private static final List<String> INTERACT_GAME_TYPE = Arrays.asList("2", "3", "4", "5", "6");      // 互动游戏type "Ludo"、"UNO"、"Monster Crush"、"Domino"、"Carrom Pool",
    private static final Map<String, Integer> GAME_TYPE_ITEM_TYPE_MAP = new HashMap<>();

    static {
        GAME_TYPE_ITEM_TYPE_MAP.put(INTERACT_GAME_TYPE.get(0), 2);
        GAME_TYPE_ITEM_TYPE_MAP.put(INTERACT_GAME_TYPE.get(1), 7);
        GAME_TYPE_ITEM_TYPE_MAP.put(INTERACT_GAME_TYPE.get(2), 5);
        GAME_TYPE_ITEM_TYPE_MAP.put(INTERACT_GAME_TYPE.get(3), 4);
        GAME_TYPE_ITEM_TYPE_MAP.put(INTERACT_GAME_TYPE.get(4), 3);
    }


    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private BackstageConfigDao backstageConfigDao;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private RoomMicDao roomMicDao;
    @Resource
    private RoomPwdRedis roomPwdRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private RoomRedis roomRedis;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Resource
    private UserLabelDao userLabelDao;
    @Resource
    private SocialListService socialListService;
    @Resource
    private RoomListRedis roomListRedis;
    @Resource
    private RoomListService roomListService;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource
    private RoomVisitorsRedis roomVisitorsRedis;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private PartyListService partyListService;
    @Resource
    private RoomEventSubDao roomEventSubDao;

    @PostConstruct
    public void postInit() {
        if (ServerConfig.isProduct()) {

        }
    }

    public SocialHomeVO socialHome(PartyListDTO dto) {
        String uid = dto.getUid();
        int page = dto.getPage() <= 0 ? 1 : dto.getPage();
        int regDay = ActorUtils.getRegDays(uid);
        SocialHomeVO vo = new SocialHomeVO();
        List<GameItemInfoVO> itemInfoList = new ArrayList<>();
        GamePlayStatusBO statusBO = this.getPlayGameStatus(uid);
        Map<Integer, Integer> allOnlineMap = gameRoomRedis.getAllOnlinePlayCount();
        int bcNum = gameRoomRedis.getGameBCNum();
        allOnlineMap.put(1052, bcNum);
        boolean showNewGame = whiteTestDao.isMemberByType(dto.getUid(), WhiteTestDao.WHITE_TYPE_RID);
        for (SociaEntryData entryData : RoomConstant.SOCIAL_ITEM_LIST) {
            if (entryData.getItemType() == 10 && (regDay <= 7 || !statusBO.isPrizeGame())) {
                continue;
            }
            // 新增游戏白名单可见
            if (entryData.getShowType() > 0 && !showNewGame) {
                continue;
            }

            GameItemInfoVO itemInfoVO = new GameItemInfoVO();
            BeanUtils.copyProperties(entryData, itemInfoVO);
            int itemOnlineCount = entryData.getGameType() > 0 ? roomVisitorsRedis.getGameRoomTypeNum(entryData.getGameCountType())
                    : allOnlineMap.getOrDefault(entryData.getGameCountType(), 0);
            itemInfoVO.setItemName(dto.getSlang() == SLangType.ENGLISH ? entryData.getItemNameEn() : entryData.getItemNameAr());
            itemInfoVO.setItemIcon(dto.getSlang() == SLangType.ENGLISH ? entryData.getIconEn() : entryData.getIconAr());
            itemInfoVO.setItemOnlineCount(itemOnlineCount);
            itemInfoVO.setLastPlay(entryData.getItemType() == statusBO.getItemType() ? 1 : 0);
            itemInfoVO.setOnlineVersion(dto.getOs() == ClientOS.IOS ? entryData.getIosVersion() : entryData.getAndroidVersion());
            itemInfoList.add(itemInfoVO);
        }
        vo.setItemInfoList(itemInfoList);

        // 测试逻辑
//        List<SocialHomeVO.FriendInfo> findFriendsList = socialListService.getFindFriendsList(ALL_COUNTRY, -1);
//        int maxSize = findFriendsList.size();
//        if (maxSize != 0) {
//            int end1 = Math.min(maxSize, 8);
//            int end2 = Math.min(maxSize, RoomListConstant.COUNTRY_PAGE_SIZE);
//            vo.setFindFriendsList(fillFriendInfoList(findFriendsList.subList(0, end2)));
//
//            List<SocialHomeVO.FriendInfo> activeRoomList = new ArrayList<>(findFriendsList);
//            PageUtils.PageData<SocialHomeVO.FriendInfo> pageData = PageUtils.getPageData(activeRoomList, page, end1);
//            vo.setRecommendUserList(fillFriendInfoList(pageData.list));
//            vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
//        } else {
//            vo.setRecommendUserList(findFriendsList);
//            vo.setFindFriendsList(findFriendsList);
//            vo.setNextUrl("");
//        }


        ActorData actorData = actorDao.getActorData(uid);
        vo.setMyCoins(actorData.getHeartGot());
        vo.setMyHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setTotalOnlineCount(getAllOnlineCount());

        String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
        boolean isNew = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
        PageUtils.PageData<ForYouListVO> pageData = socialListService.getHomeRecommendListPage(isNew ? 1 : 2, countryCode, page);
        List<ForYouListVO> list = new ArrayList<>(pageData.list);
        // 被列入房间黑名单的房间和被房间踢出的房间不推荐
        list.removeIf(item -> roomKickRedis.isKick(item.getRoomId(), uid)
                || roomBlacklistDao.isBlock(item.getRoomId(), uid)
                || item.getMicUserAid().equals(uid));
        vo.setRecommendUserList(fillFriendInfoListByForYou(isNew ? 1 : 2, list));
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");

        PageUtils.PageData<ForYouListVO> meetPageData = socialListService.getHomeRecommendListPage(isNew ? 3 : 4, countryCode, 1);
        List<ForYouListVO> meetList = new ArrayList<>(meetPageData.list);
        // 被列入房间黑名单的房间和被房间踢出的房间不推荐
        meetList.removeIf(item -> roomKickRedis.isKick(item.getRoomId(), uid)
                || roomBlacklistDao.isBlock(item.getRoomId(), uid)
                || item.getMicUserAid().equals(uid));
        vo.setFindFriendsList(fillFriendInfoListByForYou(meetList));

        return vo;
    }


    private GamePlayStatusBO getPlayGameStatus(String uid) {
        GamePlayStatusBO statusBO = new GamePlayStatusBO();
        Map<String, Integer> allPlayGameTimeMap = gameRoomRedis.getAllLastPlayGameTime(uid);
        int last15Time = DateHelper.getNowSeconds() - 15 * 86400;
        for (String gameType : GameRoomRedis.PRIZE_GAME_TYPE) {
            int playTime = allPlayGameTimeMap.getOrDefault(gameType, 0);
            if (playTime > last15Time) {
                statusBO.setPrizeGame(true);
                break;
            }
        }

        // 新用户在社交页签到展示动画
        // https://www.tapd.cn/20792731/prong/stories/view/1120792731001016870?from_iteration_id=1120792731001000360
        int regDay = ActorUtils.getRegDays(uid);
        String gameLastType = INTERACT_GAME_TYPE.get(4);
        int playLastTime = 0;
        for (String gameType : INTERACT_GAME_TYPE) {
            int playTime = allPlayGameTimeMap.getOrDefault(gameType, 0);
            if (playTime > playLastTime) {
                playLastTime = playTime;
                gameLastType = gameType;
            }
        }
        int itemType = GAME_TYPE_ITEM_TYPE_MAP.getOrDefault(gameLastType, 0);
        statusBO.setItemType(regDay <= 2 ? (regDay == 1 ? 2 : itemType) : itemType);
        return statusBO;
    }

    private List<SocialHomeVO.FriendInfo> fillFriendInfoListByForYou(List<ForYouListVO> srcList) {
        return fillFriendInfoListByForYou(null, srcList);
    }

    private List<SocialHomeVO.FriendInfo> fillFriendInfoListByForYou(Integer opt, List<ForYouListVO> srcList) {
        List<SocialHomeVO.FriendInfo> toFriendsList = new ArrayList<>();
        srcList.forEach(item -> {
            SocialHomeVO.FriendInfo toData = new SocialHomeVO.FriendInfo();
            String aid = item.getMicUserAid();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            toData.setAid(aid);
            toData.setName(actorData.getName());
            toData.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            toData.setGender(item.getMicUserGender());
            toData.setInRoomId(item.getRoomId());
            toData.setCountry(ActorUtils.getCountryCode(actorData.getCountry()));
            toData.setLabelInfoBeanList(socialListService.getLabelInfos(aid, actorData.getLabelList()));
            if (opt != null && opt == 1) {
                if (item.getSocialWeight() >= RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                    toData.setIsNewRookieRoom(1);
                } else {
                    toData.setIsNewRookieRoom(0);
                }
            }
            toFriendsList.add(toData);
        });
        return toFriendsList;
    }

    private List<SocialHomeVO.FriendInfo> fillFriendInfoList(List<SocialHomeVO.FriendInfo> srcList) {
//        List<SocialHomeVO.FriendInfo> toFriendsList = new ArrayList<>();
        srcList.forEach(item -> {
            String aid = item.getAid();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            item.setLabelInfoBeanList(socialListService.getLabelInfos(aid, actorData.getLabelList()));
//            SocialHomeVO.FriendInfo toData = new SocialHomeVO.FriendInfo();
//            BeanUtils.copyProperties(item, toData);
//            toFriendsList.add(toData);
        });
        return null;
    }

    public ListVO getFindFriendListByCountry(PartyListDTO req) {
        if (StringUtils.isEmpty(req.getCountryCode())) {
            throw new CommonException(RoomListHttpCode.PARAM_ERROR);
        }
        String countryCode = req.getCountryCode().toLowerCase();
        String uid = req.getUid();
        int page = 0 == req.getPage() ? 1 : req.getPage();
        ListVO vo = new ListVO();
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        boolean isNew = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
        if (ALL_COUNTRY.equalsIgnoreCase(countryCode)) {
            countryCode = ActorUtils.getCountryCode(actorData.getCountry());
            PageUtils.PageData<ForYouListVO> meetPageData = socialListService.getHomeRecommendListPage(isNew ? 3 : 4, countryCode, page);
            List<ForYouListVO> meetList = new ArrayList<>(meetPageData.list);
            // 被列入房间黑名单的房间和被房间踢出的房间不推荐
            meetList.removeIf(item -> roomKickRedis.isKick(item.getRoomId(), uid)
                    || roomBlacklistDao.isBlock(item.getRoomId(), uid)
                    || item.getMicUserAid().equals(uid));
            vo.setList(fillFriendInfoListByForYou(meetList));
            vo.setNextUrl(meetPageData.nextPage == 0 ? "" : meetPageData.nextPage + "");
        } else {
            int toGender = actorData.getFb_gender() == 1 ? 2 : -1;
            List<SocialHomeVO.FriendInfo> findFriendsList = socialListService.getFindFriendsList(countryCode, toGender);
            List<SocialHomeVO.FriendInfo> activeRoomList = new ArrayList<>(findFriendsList);
            PageUtils.PageData<SocialHomeVO.FriendInfo> pageData = PageUtils.getPageData(activeRoomList, page, RoomListConstant.COUNTRY_PAGE_SIZE);
            fillFriendInfoList(pageData.list);
            // 被列入房间黑名单的房间和被房间踢出的房间不推荐
            pageData.list.removeIf(item -> roomKickRedis.isKick(item.getInRoomId(), uid)
                    || roomBlacklistDao.isBlock(item.getInRoomId(), uid)
                    || item.getAid().equals(uid));
            vo.setList(pageData.list);
            vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        }
        return vo;
    }

    public ListVO getNewList(PartyListDTO req) {
        int page = req.getPage() <= 0 ? 1 : req.getPage();
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorData(uid);
        ListVO vo = new ListVO();
        String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
        boolean isNew = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
        PageUtils.PageData<ForYouListVO> allNewPageData = socialListService.getHomeRecommendListPage(isNew ? 5 : 6, countryCode, page);
        List<ForYouListVO> allNewList = new ArrayList<>(allNewPageData.list);
        // 被列入房间黑名单的房间和被房间踢出的房间不推荐
        allNewList.removeIf(item -> roomKickRedis.isKick(item.getRoomId(), uid) || roomBlacklistDao.isBlock(item.getRoomId(), uid));
//        allNewList.forEach(item -> {
//            if (isNew) {
//                logger.info("all-new roomId:{} roomName:{} roomRecommendType:{} socialWeight:{} newRookieRoomWeight:{}", item.getRoomId(), item.getRoom_name(),item.getRoomRecommendType(),
//                        item.getSocialWeight(), item.getNewRookieRoomWeight());
//            }
//            if (RoomVisitorsRedis.SHOW_VISITOR) {
//                item.setOnline(item.getVisitorNum());
//            }
//        });
        roomListService.fillRoomHeadList(allNewList, req.getSlang());
        vo.setList(allNewList);
        vo.setNextUrl(allNewPageData.nextPage == 0 ? "" : allNewPageData.nextPage + "");
        return vo;
    }

    @Cacheable(value = "doGetNewList", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public ListVO doGetNewList(int slang, int page) {
        ListVO vo = new ListVO();
        List<ForYouListVO> forYouList = roomListRedis.getActiveRoomList(false);
        forYouList.forEach(item -> {
            if (RoomVisitorsRedis.SHOW_VISITOR) {
                item.setOnline(item.getVisitorNum());
            }
        });
        PageUtils.PageData<ForYouListVO> pageData = PageUtils.getPageData(forYouList, page, RoomListConstant.FOR_YOU_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        vo.setList(pageData.list);
        roomListService.fillRoomHeadList(pageData.list, slang);
        return vo;
    }


    @Cacheable(value = "discoverList", key = "T(String).valueOf(#p0.page).concat('-').concat(#p0.slang)"
            , cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public ListVO discoverList(PartyListDTO req) {
        ListVO vo = new ListVO();
        int slang = req.getSlang();
        int page = req.getPage() <= 0 ? 1 : req.getPage();

        if (page == 1) {
            List<RoomEventVO> roomEventVOList = roomListRedis.getEventList(RoomListConstant.EVENT_COMING_SOON_AND_PARTYING);
            List<Integer> allMineSubList = roomEventSubDao.getAllMineSubList(req.getUid(), DateHelper.getNowSeconds());
            roomEventVOList.forEach(item -> {
                item.setIsSubscribed(allMineSubList.contains(item.getEventId()) ? 1 : 0);
                if (RoomVisitorsRedis.SHOW_VISITOR) {
                    item.setOnline(item.getVisitorNum());
                }
            });
            vo.setList(roomEventVOList);
        }

        List<PartyListData> featuredList = roomListRedis.getAllOnlyOnlineRoomList();
        PageUtils.PageData<PartyListData> pageData = PageUtils.getPageData(featuredList, page, RoomListConstant.DISCOVER_POPULAR_ROOMS_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        pageData.list.forEach(item -> {
            if (RoomVisitorsRedis.SHOW_VISITOR) {
                item.setOnline(item.getVisitorNum());
            }
        });
        vo.setFeaturedList(pageData.list);
        return vo;
    }

    public ListVO gameListByType(PartyListDTO req) {
        ListVO vo = new ListVO();
        int page = req.getPage() <= 0 ? 1 : req.getPage();
        int gameType = Math.max(req.getGameType(), 0);
        List<GameListVO> gameList;
        if (gameType == 0) {
            gameList = roomListRedis.getAllGameList();
        } else {
            gameList = roomListRedis.getGameList(gameType);
        }
        if (page == 1) {
            Map<String, Integer> mapCount = roomListRedis.getGameTypeDataAll();
            Map<String, Integer> allMatchGameTimeMap = gameRoomRedis.getAllMatchGameTime(req.getUid());
            List<GameItemInfoVO> itemInfoList = new ArrayList<>();
            boolean showNewGame = whiteTestDao.isMemberByType(req.getUid(), WhiteTestDao.WHITE_TYPE_RID);
            for (SociaEntryData entryData : RoomConstant.GAME_ITEM_MATCH_LIST) {
                // 新增游戏白名单可见
                if (entryData.getShowType() > 0 && !showNewGame) {
                    continue;
                }
                int appVersion = entryData.getAppVersion();
                if (!AppVersionUtils.versionCheck(appVersion, req)){
                    continue;
                }
                GameItemInfoVO itemInfoVO = new GameItemInfoVO();
                BeanUtils.copyProperties(entryData, itemInfoVO);
                itemInfoVO.setItemName(req.getSlang() == SLangType.ENGLISH ? entryData.getItemNameEn() : entryData.getItemNameAr());
                itemInfoVO.setItemIcon(req.getSlang() == SLangType.ENGLISH ? entryData.getIconEn() : entryData.getIconAr());
                itemInfoVO.setItemOnlineCount(mapCount.getOrDefault(String.valueOf(itemInfoVO.getItemType()), 0));
                itemInfoVO.setOnlineVersion(req.getOs() == ClientOS.IOS ? entryData.getIosVersion() : entryData.getAndroidVersion());
                itemInfoVO.setLastMatchTime(allMatchGameTimeMap.getOrDefault(String.valueOf(entryData.getGameType()), 0));
                itemInfoList.add(itemInfoVO);
            }
            itemInfoList.sort(Comparator.comparing(GameItemInfoVO::getLastMatchTime).reversed());
            vo.setItemInfoList(itemInfoList);
        }
        gameList.forEach(item -> {
            if (RoomVisitorsRedis.SHOW_VISITOR) {
                item.setOnline(item.getVisitorNum());
            }
        });
        List<GameListVO> lastGameList = AppVersionUtils.versionCheck(862, req) ? gameList : gameList.stream().filter(item -> item.getGame_type() != 100).collect(Collectors.toList());
        PageUtils.PageData<GameListVO> pageData = PageUtils.getPageData(lastGameList, page, RoomListConstant.GAME_PAGE_SIZE);
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        vo.setList(pageData.list);
        return vo;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<SocialHomeVO.FriendInfo> getFindFriendsList(String countryCode, int gender) {
//        Set<String> roomActors = roomPlayerRedis.getRoomActors(roomId);
//        Set<String> roomSet = roomRedis.getRoomSet();
        List<SocialHomeVO.FriendInfo> findFriendsList = new ArrayList<>(256);
        long timeMillis = System.currentTimeMillis();
        List<RoomMicData> allOnMicUserList;
        if (ALL_COUNTRY.equalsIgnoreCase(countryCode)) {
            allOnMicUserList = roomMicDao.getAllOnMicUserList(200);
        } else {
            allOnMicUserList = roomMicDao.getAllOnMicUserListByCountry(gender, countryCode);
        }
        if (CollectionUtils.isEmpty(allOnMicUserList)) {
            logger.info("allOnMicUserList is empty.");
            return findFriendsList;
        }

        for (RoomMicData micData : allOnMicUserList) {
            // 过滤上锁房间
            if (roomPwdRedis.hasPwd(micData.getRoomId())) {
                continue;
            }

            if (RoomUtils.isGameRoom(micData.getRoomId())) {
                continue;
            }
//            if (whiteTestDao.isMemberByType(micData.getRoomId(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
//                continue;
//            }
            ActorData actorData = actorDao.getActorDataFromCache(micData.getUid());
            if (actorData == null) {
                continue;
            }
            if (!ALL_COUNTRY.equalsIgnoreCase(countryCode) && !ActorUtils.isSameCountry(countryCode, actorData.getCountry())) {
                continue;
            }
            SocialHomeVO.FriendInfo data = new SocialHomeVO.FriendInfo();
            data.setAid(micData.getUid());
            data.setName(actorData.getName());
            data.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            data.setGender(actorData.getFb_gender());
            data.setInRoomId(micData.getRoomId());
            data.setCountry(ActorUtils.getCountryCode(actorData.getCountry()));
//            data.setLabelInfoBeanList(getLabelInfos(micData.getUid(), actorData.getLabelList()));
            findFriendsList.add(data);
        }
        logger.info("getFindFriendsList. findFriendsList.size={} cost={}", findFriendsList.size(), System.currentTimeMillis() - timeMillis);
        return findFriendsList;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public int getAllOnlineCount() {
        return userOnlineRedis.getAllUserOnline().size();
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name +'-'+#p0")
    public List<SocialHomeVO.LabelInfoBeanVO> getLabelInfos(String aid, List<Integer> myLabelList) {
        List<SocialHomeVO.LabelInfoBeanVO> labelList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(myLabelList)) {
            List<UserLabelData> userLabelDatas = userLabelDao.getUserLabelFromRedis(aid, myLabelList);
            for (UserLabelData data : userLabelDatas) {
                SocialHomeVO.LabelInfoBeanVO labelInfoBeanVO = new SocialHomeVO.LabelInfoBeanVO();
                labelInfoBeanVO.setLabelId(data.getLabelId());
                labelInfoBeanVO.setLabelName(data.getLabelName());
                labelInfoBeanVO.setLabelArName(data.getLabelArname());
                labelInfoBeanVO.setLabelColor(data.getRgba());
                labelList.add(labelInfoBeanVO);
            }
        }
        return labelList;
    }

    /**
     * @param opt         1 推荐用户 首页星空-新用户 2 首页星空-老用户
     *                    3 推荐用户 meet-all-新用户 4  meet-all-老用户
     *                    5 推荐房间 all-recommend-新用户 6 all-recommend 老用户（老的for you改版）
     * @param countryCode
     * @param page
     * @return
     */
//    @Cacheable(value = "getHomeRecommendListPage", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE,
//            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public PageUtils.PageData<ForYouListVO> getHomeRecommendListPage(int opt, String countryCode, int page) {
        List<ForYouListVO> homeRecommendList = new ArrayList<>();
        int pageSize = 8;
        if (opt == 1 || opt == 2) {
            pageSize = 8;
            homeRecommendList = socialListService.getHomeRecommendList(opt, countryCode);
        } else if (opt == 3 || opt == 4) {
            pageSize = RoomListConstant.COUNTRY_PAGE_SIZE;
            homeRecommendList = socialListService.getMeetAllList(opt, countryCode);

        } else if (opt == 5 || opt == 6) {
            pageSize = RoomListConstant.COUNTRY_PAGE_SIZE;
            homeRecommendList = socialListService.getAllNewList(opt, countryCode);
        }
        List<ForYouListVO> activeRoomList = new ArrayList<>(homeRecommendList);
        PageUtils.PageData<ForYouListVO> pageData = PageUtils.getPageData(activeRoomList, page, pageSize);
        return pageData;
    }

    /**
     * 推荐用户
     *
     * @param opt         1 首页星空-新用户 2 首页星空-老用户
     * @param countryCode
     * @return
     */
    @Cacheable(value = "getHomeRecommendList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<ForYouListVO> getHomeRecommendList(int opt, String countryCode) {
        List<ForYouListVO> homeRecommendList = roomListRedis.getSocialRoomList();
        if (CollectionUtils.isEmpty(homeRecommendList)) {
            return Collections.emptyList();
        }
        Iterator<ForYouListVO> iterator = homeRecommendList.iterator();
        int area = RoomListConstant.MAJOR_COUNTRY_SET.contains(countryCode) ?
                RoomListConstant.MAJOR_COUNTRY : RoomListConstant.OTHER_COUNTRY;
        JSONObject rookieRoomConfig = roomListRedis.getAllNewRookieRoomScoreWeight();
        while (iterator.hasNext()) {
            ForYouListVO partyListData = iterator.next();
            if (opt == 2) {
                // 移除迎新房
                if (partyListData.getSocialWeight() >= RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                    iterator.remove();
                    continue;
                }
            }
            // 麦位没人
            if (StringUtils.isEmpty(partyListData.getMicUserAid())) {
                iterator.remove();
                continue;
            }
            String roomCountry = ActorUtils.getCountryCode(partyListData.getCountry());
            if (ActorUtils.isSameCountry(countryCode, roomCountry)) {
                if (partyListData.getSocialWeight() != RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                    partyListData.setSocialWeight(partyListData.getSocialWeight() + RoomListConstant.SOCIAL_SAME_COUNTRY_WEIGHT);
                } else {
                    partyListData.setNewRookieRoomWeight(partyListData.getNewRookieRoomWeight() + partyListService.getSameCountryWeight(rookieRoomConfig));
                }
            } else if (area == RoomListConstant.MAJOR_COUNTRY &&
                    RoomListConstant.MAJOR_COUNTRY_SET.contains(roomCountry)) {
                if (partyListData.getSocialWeight() != RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                    partyListData.setSocialWeight(partyListData.getSocialWeight() + RoomListConstant.SOCIAL_SAME_AREA_WEIGHT);
                } else {
                    partyListData.setNewRookieRoomWeight(partyListData.getNewRookieRoomWeight() + +partyListService.getSameAreaWeight(rookieRoomConfig));
                }
            } else if (area != RoomListConstant.MAJOR_COUNTRY &&
                    !RoomListConstant.MAJOR_COUNTRY_SET.contains(roomCountry)) {
                if (partyListData.getSocialWeight() != RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                    partyListData.setSocialWeight(partyListData.getSocialWeight() + RoomListConstant.SOCIAL_SAME_AREA_WEIGHT);
                } else {
                    partyListData.setNewRookieRoomWeight(partyListData.getNewRookieRoomWeight() + partyListService.getSameAreaWeight(rookieRoomConfig));
                }
            }
        }
        homeRecommendList.sort(Comparator.comparing(ForYouListVO::getSocialWeight).reversed()
                .thenComparing(Comparator.comparing(ForYouListVO::getNewRookieRoomWeight).reversed()));
        return homeRecommendList;
    }

    /**
     * 推荐用户
     *
     * @param opt         3 meet-all-新用户 4  meet-all-老用户
     * @param countryCode
     * @return
     */
    @Cacheable(value = "getMeetAllList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<ForYouListVO> getMeetAllList(int opt, String countryCode) {
        List<ForYouListVO> homeRecommendList = null;
        if (opt == 3) {
            homeRecommendList = roomListRedis.getSocialRoomList();
        } else {
            homeRecommendList = roomListRedis.getAllNewOldUserList(true);
        }
        if (CollectionUtils.isEmpty(homeRecommendList)) {
            return Collections.emptyList();
        }
        List<ForYouListVO> activeRoomList = homeRecommendList;
        Iterator<ForYouListVO> iterator = activeRoomList.iterator();
        int area = RoomListConstant.MAJOR_COUNTRY_SET.contains(countryCode) ?
                RoomListConstant.MAJOR_COUNTRY : RoomListConstant.OTHER_COUNTRY;
        JSONObject allNewOldMeetScoreWeight = roomListRedis.getAllNewOldUserMeetScoreWeight();
        JSONObject rookieRoomConfig = roomListRedis.getAllNewRookieRoomScoreWeight();
        while (iterator.hasNext()) {
            ForYouListVO partyListData = iterator.next();
            // 麦位没人
            if (StringUtils.isEmpty(partyListData.getMicUserAid())) {
                iterator.remove();
                continue;
            }
            String roomCountry = ActorUtils.getCountryCode(partyListData.getCountry());
            if (opt == 3) {
                if (ActorUtils.isSameCountry(countryCode, roomCountry)) {
                    if (partyListData.getSocialWeight() != RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                        partyListData.setSocialWeight(partyListData.getSocialWeight() + RoomListConstant.SOCIAL_SAME_COUNTRY_WEIGHT);
                    } else {
                        partyListData.setNewRookieRoomWeight(partyListData.getNewRookieRoomWeight() + partyListService.getSameCountryWeight(rookieRoomConfig));
                    }
                } else if (area == RoomListConstant.MAJOR_COUNTRY &&
                        RoomListConstant.MAJOR_COUNTRY_SET.contains(roomCountry)) {
                    if (partyListData.getSocialWeight() != RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                        partyListData.setSocialWeight(partyListData.getSocialWeight() + RoomListConstant.SOCIAL_SAME_AREA_WEIGHT);
                    } else {
                        partyListData.setNewRookieRoomWeight(partyListData.getNewRookieRoomWeight() + partyListService.getSameAreaWeight(rookieRoomConfig));
                    }
                } else if (area != RoomListConstant.MAJOR_COUNTRY &&
                        !RoomListConstant.MAJOR_COUNTRY_SET.contains(roomCountry)) {
                    if (partyListData.getSocialWeight() != RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                        partyListData.setSocialWeight(partyListData.getSocialWeight() + RoomListConstant.SOCIAL_SAME_AREA_WEIGHT);
                    } else {
                        partyListData.setNewRookieRoomWeight(partyListData.getNewRookieRoomWeight() + partyListService.getSameAreaWeight(rookieRoomConfig));
                    }
                }
            } else {
                if (ActorUtils.isSameCountry(countryCode, roomCountry)) {
                    partyListData.setAllNewMeetWeight(partyListData.getAllNewMeetWeight()
                            + partyListService.getSameCountryWeight(allNewOldMeetScoreWeight));
                } else if (area == RoomListConstant.MAJOR_COUNTRY &&
                        RoomListConstant.MAJOR_COUNTRY_SET.contains(roomCountry)) {
                    partyListData.setAllNewMeetWeight(partyListData.getAllNewMeetWeight()
                            + partyListService.getSameAreaWeight(allNewOldMeetScoreWeight));
                } else if (area != RoomListConstant.MAJOR_COUNTRY &&
                        !RoomListConstant.MAJOR_COUNTRY_SET.contains(roomCountry)) {
                    partyListData.setAllNewMeetWeight(partyListData.getAllNewMeetWeight()
                            + partyListService.getSameAreaWeight(allNewOldMeetScoreWeight));
                }
            }

        }
        if (opt == 3) {
            activeRoomList.sort(Comparator.comparing(ForYouListVO::getSocialWeight).reversed()
                    .thenComparing(Comparator.comparing(ForYouListVO::getNewRookieRoomWeight).reversed()));
        } else {
            activeRoomList.sort(Comparator.comparing(ForYouListVO::getAllNewMeetWeight).reversed());
        }
        return activeRoomList;
    }


    /**
     * 推荐房间
     *
     * @param opt         5 推荐房间 all-recommend-新用户 6 all-recommend 老用户（老的for you改版）
     * @param countryCode
     * @return
     */
    @Cacheable(value = "getAllNewList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<ForYouListVO> getAllNewList(int opt, String countryCode) {
        List<ForYouListVO> homeRecommendList = null;
        if (opt == 5) {
            homeRecommendList = roomListRedis.getSocialRoomList();
        } else {
            homeRecommendList = roomListRedis.getAllRecommendOldUserList();
        }
        if (CollectionUtils.isEmpty(homeRecommendList)) {
            return Collections.emptyList();
        }

        List<ForYouListVO> activeRoomList = homeRecommendList;
        Iterator<ForYouListVO> iterator = activeRoomList.iterator();
        int area = RoomListConstant.MAJOR_COUNTRY_SET.contains(countryCode) ?
                RoomListConstant.MAJOR_COUNTRY : RoomListConstant.OTHER_COUNTRY;
        // JSONObject allNewOldScoreWeight = roomListRedis.getAllNewOldUserScoreWeight();
        // JSONObject rookieRoomConfig = roomListRedis.getAllNewRookieRoomScoreWeight();
        Set<String> officialWelcomeRooms = newRookieRoomRedis.getOfficialWelcomeRoomsByCountry(countryCode);
        JSONObject newRoomConfig = roomListRedis.getAllNewRoomScoreWeight();
//        logger.info("all-new newRoomConfig:{}", newRoomConfig);
        while (iterator.hasNext()) {
            ForYouListVO partyListData = iterator.next();
            String roomCountry = ActorUtils.getCountryCode(partyListData.getCountry());
            int roomArea = RoomListConstant.MAJOR_COUNTRY_SET.contains(roomCountry) ?
                    RoomListConstant.MAJOR_COUNTRY : RoomListConstant.OTHER_COUNTRY;
            if (opt == 5) {
                int socialWeight = partyListData.getSocialWeight();
                if (socialWeight == RoomListConstant.SOCIAL_OFFICIAL_ROOM_WEIGHT && !officialWelcomeRooms.contains(partyListData.getRoomId())){
                    iterator.remove();
                }

                if (ActorUtils.isSameCountry(countryCode, roomCountry)) {
                    // 同国家
                    if (1 == newRoomConfig.getIntValue("sameCountryEnable")) {
                        int weight = newRoomConfig.getIntValue("sameCountry") * newRoomConfig.getIntValue("sameCountryWeight") / 100;
                        partyListData.setRateWeight(partyListData.getRateWeight() + weight);
                    }
                }

                // logger.info("all-new-1 roomCountry:{} roomArea:{} countryCode:{} area:{} weight:{}", roomCountry, roomArea, countryCode, area, partyListData.getRateWeight());
                if (area == roomArea) {
                    // 同地区
                    if (1 == newRoomConfig.getIntValue("sameAreaEnable")) {
                        int weight = newRoomConfig.getIntValue("sameArea") * newRoomConfig.getIntValue("sameAreaWeight") / 100;
                        partyListData.setRateWeight(partyListData.getRateWeight() + weight);
                    }
                }
                // logger.info("all-new-2 roomCountry:{} roomArea:{} countryCode:{} area:{} weight:{}", roomCountry, roomArea, countryCode, area, partyListData.getRateWeight());

            } else {
                int socialWeight = partyListData.getSocialWeight();
                if (ActorUtils.isSameCountry(countryCode, roomCountry)) {
                    partyListData.setSocialWeight(socialWeight + RoomListConstant.SOCIAL_SAME_COUNTRY_WEIGHT);
                    // partyListData.setRateWeight(partyListData.getRateWeight() + partyListService.getSameCountryWeight(allNewOldScoreWeight));
                } else if (area == roomArea) {
                    partyListData.setSocialWeight(socialWeight + RoomListConstant.SOCIAL_SAME_AREA_WEIGHT);
                    // partyListData.setRateWeight(partyListData.getRateWeight() + partyListService.getSameAreaWeight(allNewOldScoreWeight));
                }
            }
        }
        if (opt == 5) {
            activeRoomList.sort(Comparator.comparing(ForYouListVO::getSocialWeight).reversed()
                    .thenComparing(Comparator.comparing(ForYouListVO::getPopularWeight).reversed())
                    .thenComparing(Comparator.comparing(ForYouListVO::getRateWeight).reversed()));
        } else {
            activeRoomList.sort(Comparator.comparing(ForYouListVO::getSocialWeight).reversed().thenComparing(Comparator.comparing(ForYouListVO::getRateWeight).reversed()));
        }
        return activeRoomList;
    }

    private boolean isNewDeviceAccount(int regDay, String firstTnId) {
        return regDay <= 7 && StringUtils.hasLength(firstTnId);
//        return regDay <= 7;
    }
}
