package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.OperationRoomWashVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.OtherSupportUserVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 分国家运营房选拔赛
 */
@Service
public class OperationRoomCountrySelectService extends OtherActivityService implements TaskMsgHandler {

    private static final Logger logger = LoggerFactory.getLogger(OperationRoomCountrySelectService.class);
    private static final String ACTIVITY_TITLE_EN = "Activity Room Countries Select Competition";
    public static final String ACTIVITY_ID = "68500a8d5e2d0c57c95d04c0";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/op_room_countries_select/?activityId=%s", ACTIVITY_ID);
    private static final List<String> ALL_LOOK_USER = Arrays.asList(); // 有权限看排行榜积分的用户
    // 已经是运营房的房间不参加本次活动
    List<String> OPERATION_ROOM_LIST = Arrays.asList(
            "r:6111b324830ba10ccf89fe70",
            "r:5c80e09c66dc63003dc4de58",
            "r:5c6cd10f66dc63516adcb6b3",
            "r:6063258e7a505b70e2bed1de",
            "r:5fafa436a3639186a55df9d8",
            "r:60d0a3a691965616273453bf",
            "r:5db8575dabb01a005f968a93",
            "r:5d04ba8babb01a005df4ea18",
            "r:64778030f0a7955a89912700",
            "r:5f9cbfd3abb01a00144e2ed7",
            "r:62d6c46f69dc68b21e2afca1",
            "r:60dd015bf4d233799ebac629",
            "r:622e9cc874bb1a9f4c763f4f",
            "r:63e410ba5ed641547f759cb0",
            "r:5ee27069484060c13abb07e8",
            "r:60254fa3abb01a0032b55944",
            "r:5cb2f98c66dc63003ebfe3f2",
            "r:5dd82e611b9cae1212012d7f",
            "r:5fb920c22a8c6468a363dbd8",
            "r:634454c6202c0143cdef7ba1",
            "r:5bc8a56266dc6300f162f3eb",
            "r:633cb648fbdf9b5e1f285947",
            "r:5ea7a1c3abb01a00856e5520",
            "r:5c190cd066dc63009d6527df",
            "r:5c334f6b66dc63011a81a3a5",
            "r:620bd31e464de81263b9c0ef",
            "r:600806c21b666cec90d405c0",
            "r:600c00bf32a61091e2d5f187",
            "r:5dcf5fb977cc7c7e1f8d4029",
            "r:5c65a64966dc63003ebe124d",
            "r:5c40423966dc630030b5faa3",
            "r:5cc9473a66dc630025bf7645",
            "r:5f6769ac15a6c3af461ca69e",
            "r:5aed7d7f1bad489359765888",
            "r:5eeba3ef48e4232b9867f454",
            "r:5db52b44ab4f1e4128e0fc96",
            "r:5d8b945178cf5ea933183b5b",
            "r:5c5cbfc066dc630026cb6262",
            "r:5f16d69babb01a004fdfe5d4",
            "r:5da4da74add87696168edb44"
    );

    // "沙特阿拉伯", "阿联酋", "伊拉克", "卡塔尔", "阿曼", "叙利亚", "阿尔及利亚", "摩洛哥", "约旦", "埃及", "巴林";
    private static final List<String> ALL_SELECT_COUNTRY_LIST = Arrays.asList("sa", "ae", "iq", "qa", "om", "sy", "dz", "ma", "jo", "eg", "bh");

//    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
//            CommonMqTaskConstant.PERSONAL_UPDATE_COUNTRY);


    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    @Resource
    private WhiteTestDao whiteTestDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            OPERATION_ROOM_LIST = Arrays.asList("r:684fedef978db62160ef9448","r:684fedeb978db62160ef9447");
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/op_room_countries_select/?activityId=%s", ACTIVITY_ID);
        }
    }


    private String getOperationRoomWashRankKey(String activityId, String countryCode) {
        return String.format("operationRoomSelect:%s:%s", activityId, countryCode);
    }

    private String getSupportRoomUserKey(String activityId, String roomId, String countryCode) {
        return String.format("supportRoomUser:%s:%s:%s", activityId, roomId, countryCode);
    }

    /**
     * 验证并获取有效的国家码
     */
    private String getValidCountryCode(String countryCode, String uid) {
        try {
            // 如果传入的countryCode为空，获取用户的实际国家码
            if (StringUtils.isEmpty(countryCode)) {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                countryCode = ActorUtils.getCountryCode(actorData.getCountry());
            }

            // 如果countryCode不在允许列表中，则使用ae作为默认值
            if (!ALL_SELECT_COUNTRY_LIST.contains(countryCode)) {
                countryCode = "ae";
            }

            return countryCode;
        } catch (Exception e) {
            logger.error("getValidCountryCode error: {}", e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
    }


    public OperationRoomWashVO operationRoomSelectConfig(String activityId, String uid, String countryCode) {
        try {
            // 验证并获取有效的国家码
            countryCode = getValidCountryCode(countryCode, uid);
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            String userCountryCode = ActorUtils.getCountryCode(actorData.getCountry());

            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
            OperationRoomWashVO vo = new OperationRoomWashVO();
            vo.setStartTime(activityData.getStartTime());
            vo.setEndTime(activityData.getEndTime());

            Map<Integer, OtherRankingListVO> hiddenRankingMap = new HashMap<>();
            OtherRankingListVO myRank = new OtherRankingListVO();
            Map<String, Integer> scoreMap = new HashMap<>();
            String hostRoomId = RoomUtils.formatRoomId(uid);

            String operationRoomWashRankKey = getOperationRoomWashRankKey(activityId, countryCode);
            Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(operationRoomWashRankKey, 10);
            int rank = 1;
            int flag = -1; // 房间在榜单中的排名,前10有值，否则为-1

            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                OtherRankingListVO rankingListVO = new OtherRankingListVO();
                String roomId = entry.getKey();
                rankingListVO.setScoreStr(entry.getValue().toString());
                rankingListVO.setRoomId(roomId);
                rankingListVO.setRank(rank);
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));

                String supportUserKey = getSupportRoomUserKey(activityId, roomId, countryCode);
                List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 4);
                List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
                for (String supportUid : supportUserList) {
                    if (supportUid.equals(RoomUtils.getRoomHostId(roomId))) {
                        continue;
                    }
                    OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                    ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                    supportUserVO.setName(supportActorData.getName());
                    supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                    supportUserVO.setUid(supportUid);
                    supportUserVOList.add(supportUserVO);

                    if (supportUserVOList.size() >= 3) {
                        break;
                    }
                }
                rankingListVO.setSupportUserList(supportUserVOList);
                if (roomId.equals(hostRoomId)) {
                    flag = rank;
                    BeanUtils.copyProperties(rankingListVO, myRank);
                    List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
                    for (OtherSupportUserVO srcSupportUser : supportUserVOList) {
                        OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                        BeanUtils.copyProperties(srcSupportUser, supportUserVO);
                        supportMyUserVOList.add(supportUserVO);
                    }
                    myRank.setSupportUserList(supportMyUserVOList);
                } else {
                    if (!ALL_LOOK_USER.contains(uid)) {
                        rankingListVO.setScoreStr("*****");
                    }
                }
                hiddenRankingMap.put(rank, rankingListVO);
                scoreMap.put(entry.getKey(), entry.getValue());
                rank += 1;
            }

            if (flag > 0) {
                OtherRankingListVO beforeMap = hiddenRankingMap.get(flag - 1);
                if (beforeMap != null) {
                    beforeMap.setScoreStr(scoreMap.get(beforeMap.getRoomId()).toString());
                }

                OtherRankingListVO afterMap = hiddenRankingMap.get(flag + 1);
                if (afterMap != null) {
                    afterMap.setScoreStr(scoreMap.get(afterMap.getRoomId()).toString());
                }
            } else {

                myRank.setScoreStr(String.valueOf(activityCommonRedis.getCommonZSetRankingScore(operationRoomWashRankKey, hostRoomId)));
                myRank.setRoomId(hostRoomId);
                myRank.setRank(-1); // 没有上榜
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(hostRoomId);
                if (roomData != null) {
                    myRank.setName(roomData.getName());
                    myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                } else {
                    myRank.setName(actorData.getName());
                    myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                }
                String supportUserKey = getSupportRoomUserKey(activityId, hostRoomId, countryCode);
                List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 4);
                List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
                for (String supportUid : supportUserList) {
                    if (supportUid.equals(RoomUtils.getRoomHostId(hostRoomId))) {
                        continue;
                    }
                    OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                    ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                    supportUserVO.setName(supportActorData.getName());
                    supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                    supportUserVO.setUid(supportUid);
                    supportMyUserVOList.add(supportUserVO);
                    if (supportMyUserVOList.size() >= 3) {
                        break;
                    }
                }
                myRank.setSupportUserList(supportMyUserVOList);
            }
            if (OPERATION_ROOM_LIST.contains(hostRoomId)) {
                myRank.setRank(-2); // 已经是运营房
            } else if (!ALL_SELECT_COUNTRY_LIST.contains(userCountryCode)) {
                myRank.setRank(-3); // 用户国家不在允许列表中
            } else if (!countryCode.equals(userCountryCode)) {
                myRank.setRank(-4); // 当前选择的国家不是用户实际国家
            }
            vo.setSelectedCountryCode(countryCode);
            vo.setMyRank(myRank);
            vo.setOtherRankingList(new ArrayList<>(hiddenRankingMap.values()));
            return vo;
        } catch (Exception e) {
            logger.error("operationRoomSelectConfig error: {}", e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }
    }

    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        try {
            int totalPrice = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            String roomId = giftData.getRoomId();
            String fromUid = giftData.getFrom_uid();

            if (StringUtils.isEmpty(roomId)) {
                return;
            }

            if (OPERATION_ROOM_LIST.contains(roomId) || RoomUtils.isGameRoom(roomId)) {
                return;
            }

            // 获取发送者的国家码
            ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));
            String countryCode = ActorUtils.getCountryCode(actorData.getCountry());

            // 只收集在允许列表中的国家的数据
            if (!ALL_SELECT_COUNTRY_LIST.contains(countryCode)) {
                return;
            }

            String operationRoomWashRankKey = getOperationRoomWashRankKey(activityId, countryCode);
            String supportRoomUserKey = getSupportRoomUserKey(activityId, roomId, countryCode);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(operationRoomWashRankKey, roomId, totalPrice);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(supportRoomUserKey, fromUid, totalPrice);
        } catch (Exception e) {
            logger.error("sendGiftHandle error: {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!CommonMqTaskConstant.PERSONAL_UPDATE_COUNTRY.equals(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }

        changeCountryHandle(uid, data);
    }

    private void changeCountryHandle(String uid, CommonMqTopicData mqData) {
        try {
            if (!checkAc(uid, mqData)) {
                return;
            }

            String changeCountry = mqData.getAid();
            if (StringUtils.isEmpty(changeCountry) || !changeCountry.contains("-")) {
                logger.error("changeCountry format invalid: uid={} changeCountry={}", uid, changeCountry);
                return;
            }

            // 解析改之前和改之后的国家码
            String[] countryCodes = changeCountry.split("-");
            if (countryCodes.length != 2) {
                logger.error("changeCountry format invalid: uid={} changeCountry={}", uid, changeCountry);
                return;
            }

            String oldCountryCode = countryCodes[0];
            String newCountryCode = countryCodes[1];

            boolean shouldMigrate = false; // 是否迁移数据，否则清空

            actorDao.removeActorCache(uid);
            // 验证国家码有效性
            if (ALL_SELECT_COUNTRY_LIST.contains(oldCountryCode) ) {
                logger.info("old country codes  in select list: uid={} oldCountryCode={} newCountryCode={}",
                        uid, oldCountryCode, newCountryCode);
                if(ALL_SELECT_COUNTRY_LIST.contains(newCountryCode)){
                    shouldMigrate = true;
                }
            }else {
                logger.info("old country codes not in select list: uid={} oldCountryCode={} newCountryCode={}",
                        uid, oldCountryCode, newCountryCode);
                return;
            }

            int vipLevel = vipInfoDao.getIntVipLevel(uid);
            String roomId = RoomUtils.formatRoomId(uid);

            // 获取改国家次数记录key
            String changeCountryCountKey = getChangeCountryCountKey(ACTIVITY_ID);
            int changeCount = activityCommonRedis.getCommonHashValue(changeCountryCountKey, uid);

            // 判断是否允许改国家
//            boolean canChange = false;
//            if (vipLevel <= 0) {
//                // vip<=0时，始终允许改国家，但清空数据
//                canChange = true;
//                shouldMigrate = false;
//            } else if (vipLevel <= 4) {
//                // vip<=4时，可改国家1次
//                if (changeCount < 1) {
//                    canChange = true;
//                    shouldMigrate = true;
//                } else {
//                    // 超过次数，清空数据
//                    canChange = true;
//                    shouldMigrate = false;
//                }
//            } else {
//                // vip>=5时，可改国家2次
//                if (changeCount < 2) {
//                    canChange = true;
//                    shouldMigrate = true;
//                } else {
//                    // 超过次数，清空数据
//                    canChange = true;
//                    shouldMigrate = false;
//                }
//            }


            // 获取原国家的数据
            String oldOperationRoomWashRankKey = getOperationRoomWashRankKey(ACTIVITY_ID, oldCountryCode);
            String oldSupportRoomUserKey = getSupportRoomUserKey(ACTIVITY_ID, roomId, oldCountryCode);

            // 获取原有数据用于备份和迁移
            int oldRoomScore = activityCommonRedis.getCommonZSetRankingScore(oldOperationRoomWashRankKey, roomId);
            // 只迁移前50名用户的数据
            Map<String, Integer> oldSupportUsersMap = activityCommonRedis.getCommonRankingMap(oldSupportRoomUserKey, 50);

            // 备份原有数据到redis hash
            String backupKey = getBackupKey(ACTIVITY_ID, uid, changeCount + 1);
            Map<String, String> backupData = new HashMap<>();
            backupData.put("oldCountryCode", oldCountryCode);
            backupData.put("newCountryCode", newCountryCode);
            backupData.put("roomScore", String.valueOf(oldRoomScore));
            backupData.put("changeTime", String.valueOf(DateHelper.getNowSeconds()));
            backupData.put("vipLevel", String.valueOf(vipLevel));
//            backupData.put("supportUsers", JSONObject.toJSONString(oldSupportUsersMap));
            activityCommonRedis.setCommonHashDataAll(backupKey, backupData);

            if (shouldMigrate && oldRoomScore > 0) {
                // 迁移数据到新国家
                String newOperationRoomWashRankKey = getOperationRoomWashRankKey(ACTIVITY_ID, newCountryCode);
                String newSupportRoomUserKey = getSupportRoomUserKey(ACTIVITY_ID, roomId, newCountryCode);

                // 迁移房间积分
                activityCommonRedis.incrCommonZSetRankingScoreSimple(newOperationRoomWashRankKey, roomId, oldRoomScore);

                // 迁移支持用户积分
                for (Map.Entry<String, Integer> entry : oldSupportUsersMap.entrySet()) {
                    String supportUid = entry.getKey();
                    int supportScore = entry.getValue();
                    if (supportScore > 0) {
                        activityCommonRedis.incrCommonZSetRankingScoreSimple(newSupportRoomUserKey, supportUid, supportScore);
                    }
                }

                logger.info("migrate country data success: uid={} roomId={} oldCountry={} newCountry={} roomScore={} supportUserCount={}",
                        uid, roomId, oldCountryCode, newCountryCode, oldRoomScore, oldSupportUsersMap.size());
            } else {
                logger.info("clear country data: uid={} roomId={} oldCountry={} newCountry={} roomScore={} supportUserCount={} shouldMigrate={}",
                        uid, roomId, oldCountryCode, newCountryCode, oldRoomScore, oldSupportUsersMap.size(), shouldMigrate);
            }

            // 清空原国家数据
            if (oldRoomScore > 0) {
                activityCommonRedis.removeCommonZSet(oldOperationRoomWashRankKey, roomId);
            }

            // 清空原支持用户数据
            activityCommonRedis.deleteCommonZSet(oldSupportRoomUserKey);
//            for (String supportUid : oldSupportUsersMap.keySet()) {
//                activityCommonRedis.removeCommonZSet(oldSupportRoomUserKey, supportUid);
//            }

            // 更新改国家次数
            activityCommonRedis.setCommonHashNum(changeCountryCountKey, uid, changeCount + 1);
            activityCommonRedis.setCommonHashData(changeCountryCountKey, uid + "_lastChangeTime", String.valueOf(DateHelper.getNowSeconds()));
            activityCommonRedis.setCommonHashData(changeCountryCountKey, uid + "_lastChange", changeCountry);

            logger.info("changeCountryHandle success: uid={} changeCountry={} vipLevel={} changeCount={} shouldMigrate={}",
                    uid, changeCountry, vipLevel, changeCount + 1, shouldMigrate);

        } catch (Exception e) {
            logger.error("changeCountryHandle error: uid={} mqData={} error={}", uid, mqData, e.getMessage(), e);
        }
    }

    /**
     * 获取改国家次数记录key
     */
    private String getChangeCountryCountKey(String activityId) {
        return String.format("changeCountryCount:%s", activityId);
    }

    /**
     * 获取备份数据key
     */
    private String getBackupKey(String activityId, String uid, int changeIndex) {
        return String.format("countryChangeBackup:%s:%s:%d", activityId, uid, changeIndex);
    }

    private boolean checkAc(String uid, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    // 总榜排行榜奖励
    public void distributionTotalRanking(String activityId) {
        try {
            // 为每个国家分别发放奖励
            for (String countryCode : ALL_SELECT_COUNTRY_LIST) {
                Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getOperationRoomWashRankKey(activityId, countryCode), 10);
                int rank = 1;
                for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                    String rankUid = RoomUtils.getRoomHostId(entry.getKey());
                    String resourceKey = null;
                    switch (rank) {
                        case 1:
                            resourceKey = "ActivityRoomCompetitionTop1";
                            break;
                        case 2:
                            resourceKey = "ActivityRoomCompetitionTop2";
                            break;
                        case 3:
                            resourceKey = "ActivityRoomCompetitionTop3";
                            break;
                        default:
                            resourceKey = "ActivityRoomCompetitionTop4-10";
                    }
                    resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, "", "");
                    rank += 1;
                }
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

}
