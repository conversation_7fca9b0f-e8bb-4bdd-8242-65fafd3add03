package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quhong.data.HotSearchListData;
import com.quhong.mysql.data.InviteFissionData;
import com.quhong.mysql.mapper.ustar.InviteFissionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
public class InviteFissionDao {

    private static final Logger logger = LoggerFactory.getLogger(InviteFissionDao.class);

    @Resource
    private InviteFissionMapper inviteFissionMapper;

    public int updateById(InviteFissionData data) {
        return inviteFissionMapper.updateById(data);
    }

    public int insert(InviteFissionData data) {
        return inviteFissionMapper.insert(data);
    }

    public InviteFissionData getOneByAid(String aid) {
        QueryWrapper<InviteFissionData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("aid", aid);
        return inviteFissionMapper.selectOne(queryWrapper);
    }

    public List<InviteFissionData> getDetailByUid(String uid) {
        QueryWrapper<InviteFissionData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        return inviteFissionMapper.selectList(queryWrapper);
    }

    public Integer getCountByUid(String uid, Integer startTime) {
        QueryWrapper<InviteFissionData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        if (startTime != null) {
            queryWrapper.ge("ctime", startTime);
        }
        return inviteFissionMapper.selectCount(queryWrapper);
    }

    public List<InviteFissionData> getRollList() {
        QueryWrapper<InviteFissionData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("ctime");
        queryWrapper.last("limit 30");
        return inviteFissionMapper.selectList(queryWrapper);
    }


    public List<HotSearchListData> getRankListData(Integer startTime) {
        return inviteFissionMapper.getRankListData(startTime);
    }

    public HotSearchListData getMyTotalData(String uid) {
        return inviteFissionMapper.getMyTotalData(uid);
    }

    public InviteFissionData getInviteByAidOrDev(String aid, String deviceId) {
        QueryWrapper<InviteFissionData> queryWrapper = Wrappers.query();
        queryWrapper.eq("aid", aid);
        queryWrapper.or().eq("device_id", deviceId);
        return inviteFissionMapper.selectOne(queryWrapper);
    }
}
