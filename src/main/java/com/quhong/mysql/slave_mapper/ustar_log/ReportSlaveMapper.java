package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.mysql.mapper.ShardingMapper;
import com.quhong.operation.share.vo.ApiReportListVO;
import com.quhong.operation.share.vo.ReportCountryVO;
import com.quhong.operation.share.vo.ReportStatVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportSlaveMapper extends ShardingMapper {

    List<ApiReportListVO> getStatReportList(@Param("tableSuffix") String tableSuffix,
                                            @Param("uid") String uid,
                                            @Param("type") Integer type,
                                            @Param("os") Integer os,
                                            @Param("name") String name,
                                            @Param("offset") Integer offset,
                                            @Param("pageSize") Integer pageSize);

    Integer getStatReportListTotal(@Param("tableSuffix") String tableSuffix,
                                   @Param("uid") String uid,
                                   @Param("type") Integer type,
                                   @Param("os") Integer os,
                                   @Param("name") String name);

    List<ReportStatVO> statInDay(@Param("tableSuffix") String tableSuffix,
                                 @Param("uid") String uid,
                                 @Param("type") Integer type,
                                 @Param("os") Integer os,
                                 @Param("name") String name,
                                 @Param("countryCode") String countryCode);

    ReportStatVO statByDay(@Param("tableSuffix") String tableSuffix,
                           @Param("uid") String uid,
                           @Param("type") Integer type,
                           @Param("os") Integer os,
                           @Param("name") String name,
                           @Param("countryCode") String countryCode);

    List<ReportCountryVO> statCountry(@Param("tableSuffix") String tableSuffix,
                                      @Param("uid") String uid,
                                      @Param("type") Integer type,
                                      @Param("os") Integer os,
                                      @Param("name") String name,
                                      @Param("countryCode") String countryCode);
}
