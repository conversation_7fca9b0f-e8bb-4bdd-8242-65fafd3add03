package com.quhong.constant;

import com.quhong.enums.HttpCode;

public class DataResourcesHttpCode extends HttpCode {
    public static final HttpCode REPEAT_CHARGE = new HttpCode(2, "repeat_charge");
    public static final HttpCode PARAMS_ERROR = new HttpCode(3, "params_error");
    public static final HttpCode NOT_FIND_RESOURCES = new HttpCode(4, "not find resources");
    public static final HttpCode NOT_OWN_RESOURCES = new HttpCode(5, "not own resources");
    public static final HttpCode OTHER_HANDLE_ERROR = new HttpCode(6, "other handle error");
    public static final HttpCode USER_NOT_EXIST = new HttpCode(7, "user not exist");
    public static final HttpCode JOIN_NOT_MATCH_USE = new HttpCode(8, "join not match use");
    public static final HttpCode BACKGROUND_NOT_MATCH_USE = new HttpCode(9, "background is default or tycoon type can not get");
    public static final HttpCode RESOURCE_NOT_MATCH_USE = new HttpCode(10, "resource not match use");
}
