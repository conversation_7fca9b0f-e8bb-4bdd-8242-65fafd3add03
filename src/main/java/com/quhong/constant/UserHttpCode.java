package com.quhong.constant;

import com.quhong.enums.HttpCode;

public class UserHttpCode extends HttpCode {
    public static final HttpCode BLACKLIST_UNAVAILABLE = new HttpCode(30, "You have already add this user into blacklist");
    public static final HttpCode NOT_BLOCK_USER = new HttpCode(30, "You did not block him/her");

    public static final HttpCode USER_NOT_VALID = new HttpCode(30, "User not valid");
    public static final HttpCode USER_NOT_EXIST = new HttpCode(31, "user_not_exist");
    public static final HttpCode USER_REJECT = new HttpCode(52, "You cannot follow him/her.");
    public static final HttpCode USER_NOT_ALLOW = new HttpCode(41, "You cannot follow yourself");
    public static final HttpCode MAX_ACCOUNT_LIMIT = new HttpCode(45, "max_account_limit");
    public static final HttpCode ALREADY_FOLLOWED = new HttpCode(30, "You have already followed this user");

    public static final HttpCode NOT_HOST_OR_VICE = new HttpCode(1001, "");
    public static final HttpCode NOT_FOUND_PURCHASABLE_RESOURCES = new HttpCode(1002, "");
    public static final HttpCode DIAMONDS_NOT_ENOUGH = new HttpCode(50, "Your diamond balance is insufficient, please recharge first.","رصيدك من الماس غير كاف ، يرجى إعادة الشحن أولاً");
    public static final HttpCode COIN_NOT_ENOUGH = new HttpCode(55, "coin_not_enough");
    public static final HttpCode BUY_BACKGROUND_FAILURE = new HttpCode(1005, "");

    public static final HttpCode MALE_VISITOR_VIP_LIMIT = new HttpCode(6001, "need Tycoon level greater than or equal to 5");
    public static final HttpCode FEMALE_VISITOR_VIP_LIMIT = new HttpCode(6001, "Open Queen privileges to unlock stealthy visit", "قم بتفعيل امتيازات الملكة لاستخدام الزيارة متخفية.");
    public static final HttpCode YOU_HAVE_SIGNED_IN = new HttpCode(6002, "you_have_signed_in");
    public static final HttpCode PLEASE_LOGIN_AGAIN = new HttpCode(6003, "please_login_again");

    public static final HttpCode NOT_HAVE_PERMISSION = new HttpCode(101, "not_have_permission");
    public static final HttpCode ROOM_LOCK_NOT_EXIST = new HttpCode(10, "this type room lock not exist");
    public static final HttpCode ROOM_LOCK_BUY_FAILED = new HttpCode(6001, "buy room lock failed");
    public static final HttpCode NOT_ENOUGH_DIAMONDS = new HttpCode(6001, "Your diamonds are not enough. Recharge now?");

    public static final HttpCode RIPPLE_SET_FAILED = new HttpCode(41, "set ripple failed");
    public static final HttpCode NOT_OWN_RIPPLE_RESOURCES = new HttpCode(41, "you do not have this ripple");

    public static final HttpCode BUY_MIC_FRAME_NOT_EXIST = new HttpCode(10, "this mic frame not exist");
    public static final HttpCode MIC_FRAME_BUY_FAILED = new HttpCode(6001, "buy mic failed");
    public static final HttpCode MIC_FRAME_BUY_SUCCESS = new HttpCode(0, "Purchase mic frame success!", "تم شراء اطار المايك بنجاح! ");

    public static final HttpCode MIC_FRAME_SET_FAILED = new HttpCode(6001, "set mic frame failed");
    public static final HttpCode VIP_MIC_FRAME_SET_FAILED = new HttpCode(6001, "Please switch to Tycoon status first", "يرجى التبديل إلى حالة عضوية التميز أولاً");
    public static final HttpCode NOT_OWN_MIC_FRAME_RESOURCES = new HttpCode(41, "you do not have this mic frame");
    public static final HttpCode MIC_FRAME_SET_SUCCESS = new HttpCode(0, "Wear mic frame success!", "تم ارتداء اطار المايك بنجاح!");

    public static final HttpCode NOT_WEAR_MIC_FRAME_RESOURCES = new HttpCode(41, "you do not wear this mic frame");
    public static final HttpCode MIC_FRAME_UN_WEAR_SUCCESS = new HttpCode(0, "Remove mic frame success", "تم إلغاء اطار المايك بنجاح!");

    public static final HttpCode BEAUTIFUL_RID_BUY_FAILED = new HttpCode(6001, "Purchase Unique ID failed!", "شراء ال ID المميز فشل! ً");
    public static final HttpCode BEAUTIFUL_RID_BUY_SUCCESS = new HttpCode(0, "Purchase Unique ID success!", "تم شراء ال ID المميز بنجاح! ");

    public static final HttpCode NOT_OWN_BUBBLE_RESOURCES = new HttpCode(41, "you do not have this bubble");
    public static final HttpCode BUBBLE_SET_FAILED = new HttpCode(6001, "set bubble failed");

    public static final HttpCode BUY_FLOAT_SCREEN_NOT_EXIST = new HttpCode(10, "this float screen not exist");
    public static final HttpCode FLOAT_SCREEN_BUY_FAILED = new HttpCode(6001, "buy float screen failed");
    public static final HttpCode FLOAT_SCREEN_BUY_SUCCESS = new HttpCode(0, "Purchase success!", "تم بنجاح! ");

    public static final HttpCode FLOAT_SCREEN_SET_FAILED = new HttpCode(6001, "set float screen failed");
    public static final HttpCode NOT_OWN_FLOAT_SCREEN_RESOURCES = new HttpCode(41, "you do not have this float screen");
    public static final HttpCode FLOAT_SCREEN_SET_SUCCESS = new HttpCode(0, "'Wear successfully.", "ارتديها بنجاح.");
    public static final HttpCode FLOAT_SCREEN_UN_WEAR_SUCCESS = new HttpCode(0, "Remove success", "تم بنجاح!");


    public static final HttpCode BUY_RIDE_NOT_EXIST = new HttpCode(10, "this join carton not exist");
    public static final HttpCode RIDE_BUY_FAILED = new HttpCode(6001, "buy join carton failed");
    public static final HttpCode RIDE_BUY_SUCCESS = new HttpCode(0, "The ride has been purchased successfully!", "تم شراء المركبة بنجاح!");

    public static final HttpCode BUY_RES_NOT_EXIST = new HttpCode(10, "this resource not exist");
    public static final HttpCode RES_BUY_FAILED = new HttpCode(6001, "buy resource failed");
    public static final HttpCode RES_BUY_SUCCESS = new HttpCode(0, "Successful purchase, please dress up in my dress up page.","عملية شراء ناجحة ، يرجى التأنق في صفحة التأنق خاصتي .");
    public static final HttpCode RES_BUY_OTHER_SUCCESS = new HttpCode(0, "Giving successful !","نجحت عملية الإعطاء!");
    public static final HttpCode RES_BUY_OTHER_FRIEND = new HttpCode(6002, "He's not your friend yet","إنه ليس صديقك بعد");


    public static final HttpCode RES_DRESS_SUCCESS = new HttpCode(0, "Dress up successfully.","تم التأنُّق بنجاح");
    public static final HttpCode RES_UNDRESS_SUCCESS = new HttpCode(0, "Undress successful.Dress up will not be shown.","تم إلغاء التأنُّق بنجاح . لن يتم عرض التأنُّق.");


    public static final HttpCode DEVICE_PROFILE_LIMIT_BAN = new HttpCode(72, "device_profile_limit_ban");
    public static final HttpCode CODE_NOT_ALLOW_CHANGE = new HttpCode(41, "Your are not allowed to change image due to violation of YouStar regulation");
    public static final HttpCode CODE_NAME_NOT_ALLOW = new HttpCode(20, "Name is not valid");
    public static final HttpCode CODE_DESC_NOT_ALLOW = new HttpCode(20, "desc is unavailable");
    public static final HttpCode CODE_HEAD_NOT_ALLOW = new HttpCode(20, "you are in reported time ,so you can not change head");
    public static final HttpCode LEN_LABEL_MIN = new HttpCode(20, "Choose 5 or more interests","اأضف 5 هوايات أو أكثر");
    public static final HttpCode LEN_LABEL_MAX = new HttpCode(20, "You've reached the maximum number of interests allowed.","لقد وصلت إلى الحد الأقصى من عدد الاهتمامات المسموح به.");
    public static final HttpCode CODE_OVER_18_YEARS_OLD = new HttpCode(20, "Age should be over 18 years old");
    public static final HttpCode CODE_CANNOT_MODIFY_GENDER = new HttpCode(20, "You Cannot Modify Gender");

    public static final HttpCode INVALID_TASK = new HttpCode(6001, "Invalid task");

    public static final HttpCode ROOM_HAS_BEEN_CONQUERED = new HttpCode(60, "room_has_been_conquered");
    public static final HttpCode VIP5_LIMIT_VIDEO = new HttpCode(6001, "vip5_limit_video");

    public static final HttpCode USER_RISK_INVALID_UPDATE_INFO = new HttpCode(61, "user_risk_invalid_update_info");

    public static final HttpCode USER_NOT_BACK= new HttpCode(6010, "Not returning to users");
    public static final HttpCode INVALID_BACK = new HttpCode(6010, "Invalid back activity");
    public static final HttpCode DAU_REWARD_FAIL = new HttpCode(6010, "dau reward fail");
    public static final HttpCode INVITE_CODE_NOT_ALLOW = new HttpCode(6010, "invite code is not valid","شيفرة الدعوة غير صالحة");
    public static final HttpCode MAX_CODE_DAY_NOT_ALLOW = new HttpCode(6010, "The user has reached the daily invitation limit, this invitation binding is invalid.","لقد وصل المستخدم إلى حد الدعوة اليومي، وربط الدعوة هذا غير صالح.");
    public static final HttpCode MAX_CODE_TOTAL_NOT_ALLOW = new HttpCode(6010, "This user has reached the maximum invitation limit, invitation binding can no longer be performed","لقد وصل هذا المستخدم إلى الحد الأقصى للدعوة، ولم يعد من الممكن تنفيذ ربط الدعوة");
    public static final HttpCode AID_DEV_NOT_ALLOW = new HttpCode(6010, "User or device has been invited","تمت دعوة المستخدم أو الجهاز");
    public static final HttpCode MYSELF_NOT_ALLOW = new HttpCode(6010, "Cannot invite yourself","لا يمكن دعوة نفسك");
    public static final HttpCode REG_NOT_ALLOW = new HttpCode(6010, "Binding relationship failed, you are not a newly registered user","فشل الربط، أنت لست مستخدم مسجل حديثا");
    public static final HttpCode COLLECT_REWARD_FAIL = new HttpCode(6010, "collect reward fail because is get already");
    public static final HttpCode MAX_CODE_MONTH_NOT_ALLOW = new HttpCode(6010, "This user has reached the monthly invitation  limit, this invitation binding is invalid.","وصل المستخدم للحد الشهري للدعوات، هذه الدعوة غير صالحة.");
    public static final HttpCode VIP_LEVEL_ENABLE_MODIFY_COUNTRY = new HttpCode(6011, "vip_level_enable_modify_country");
}
