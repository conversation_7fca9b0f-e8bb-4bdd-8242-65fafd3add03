baseUrl: /vip/
server:
  port: 8080
spring:
  application:
    name: ustar-java-vip
  messages:
    basename: i18n/vip
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 3000
